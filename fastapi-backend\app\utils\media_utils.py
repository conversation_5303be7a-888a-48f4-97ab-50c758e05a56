from fastapi import HTT<PERSON>Exception # type: ignore
from fastapi.responses import StreamingResponse, Response # type: ignore
from core.nas_connector import get_ftp_connector
from core.config import get_settings, ITEMS_PER_PAGE
import os
import logging
import re
from datetime import datetime
import asyncio
import base64
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from sqlalchemy import select

logger = logging.getLogger('media_utils')

# All media file extensions
IMAGE_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.tiff', '.svg'}
AUDIO_EXTENSIONS = {'.mp3', '.wav', '.flac', '.ogg', '.m4a', '.aac', '.wma', '.opus'}
TEXT_EXTENSIONS = {'.txt', '.md', '.json', '.xml', '.log', '.yaml', '.yml', '.csv'}
CSV_EXTENSIONS = {'.csv', '.xlsx', '.xls'}
PDF_EXTENSIONS = {'.pdf'}
VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv'}

# File size threshold for Redis caching (10MB)
REDIS_CACHE_THRESHOLD = 10 * 1024 * 1024  # 10MB

# In-flight request tracking to prevent duplicate downloads
_downloading_files = {}

def natural_sort_key(item):
    """Natural sort key for sorting media names (works with strings or dicts with 'name' key)"""
    name = item.get('name', '') if isinstance(item, dict) else str(item)
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', name)]

async def get_media_from_folder(folder_path: str, content_type: str = "image", page: int = 1, items_per_page: int = None, recursive: bool = True):
    """Get media files (images, videos, audio, text, pdf) from a folder with pagination"""
    from core.config import ITEMS_PER_PAGE
    
    # Use default pagination setting if not specified
    if items_per_page is None:
        items_per_page = ITEMS_PER_PAGE
    
    # Define extensions based on content type
    if content_type == "video":
        allowed_extensions = VIDEO_EXTENSIONS
    elif content_type == "audio":
        allowed_extensions = AUDIO_EXTENSIONS
    elif content_type == "text":
        allowed_extensions = TEXT_EXTENSIONS
    elif content_type == "csv":
        allowed_extensions = CSV_EXTENSIONS
    elif content_type == "pdf":
        allowed_extensions = PDF_EXTENSIONS
    else:  # content_type == "image"
        allowed_extensions = IMAGE_EXTENSIONS
    
    try:
        if not folder_path:
            return [], 0

        connector = await get_ftp_connector()
        if not connector:
            return [], 0

        folder_path = folder_path.strip()
        folder_path = '/' + folder_path.lstrip('/')

        items = await connector.list_directory(folder_path)

        if isinstance(items, dict) and 'data' in items:
            items = items.get('data', {}).get('files', [])

        directories, files = [], []
        for item in items:
            if not isinstance(item, dict) or 'name' not in item:
                continue

            if 'path' not in item:
                item['path'] = os.path.join(folder_path, item['name']).replace('\\', '/')

            item_type = item.get('type', '').lower()
            if item_type in ('directory', 'dir', 'folder'):
                directories.append(item)
            elif item_type == 'file':
                files.append(item)

        # Filter for the specified content type
        media_files = [f for f in files if os.path.splitext(f['name'])[1].lower() in allowed_extensions]

        if recursive and directories:
            for directory in directories:
                subdir_media, _ = await get_media_from_folder(
                    directory['path'], content_type=content_type, page=1, items_per_page=50000, recursive=True
                )
                media_files.extend(subdir_media)

        media_files.sort(key=natural_sort_key)
        total_media = len(media_files)
        start_idx = (page - 1) * items_per_page
        paginated_media = media_files[start_idx:start_idx + items_per_page]

        return paginated_media, total_media

    except Exception as e:
        logger.error(f"Error getting {content_type} files from folder {folder_path}: {str(e)}")
        return [], 0

async def detect_folder_content_type(folder_path: str):
    """Detect if a folder contains primarily images, videos, audio, text, or PDFs"""
    try:
        
        connector = await get_ftp_connector()
        if not connector:
            return "unknown"

        items = await connector.list_directory(folder_path)
        if isinstance(items, dict) and 'data' in items:
            items = items.get('data', {}).get('files', [])

        if not items:
            return "empty"

        # Count different file types  
        type_counts = {'image': 0, 'video': 0, 'audio': 0, 'text': 0, 'pdf': 0, 'csv': 0}
        
        for item in items:
            if isinstance(item, dict) and item.get('type') == 'file':
                ext = os.path.splitext(item.get('name', ''))[1].lower()
                if ext in IMAGE_EXTENSIONS:
                    type_counts['image'] += 1
                elif ext in VIDEO_EXTENSIONS:
                    type_counts['video'] += 1
                elif ext in AUDIO_EXTENSIONS:
                    type_counts['audio'] += 1
                elif ext in TEXT_EXTENSIONS:
                    type_counts['text'] += 1
                elif ext in CSV_EXTENSIONS:
                    type_counts['csv'] += 1
                elif ext in PDF_EXTENSIONS:
                    type_counts['pdf'] += 1

        # Return the dominant type or "mixed" if multiple types
        if sum(type_counts.values()) == 0:
            return "unknown"
        
        non_zero_types = [k for k, v in type_counts.items() if v > 0]
        if len(non_zero_types) > 1:
            return "mixed"
        
        return max(type_counts, key=type_counts.get)
            
    except Exception as e:
        logger.error(f"Error detecting content type for {folder_path}: {str(e)}")
        return "unknown"

async def get_storage_connector_for_project(project_code: str = None):
    """Get the appropriate storage connector (NAS or MinIO) for a project with connection pooling"""
    if not project_code:
        # Fallback to default NAS connector if no project specified
        return await get_ftp_connector()
    
    try:
        
        async with get_master_db_context() as session:
            result = await session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            )
            project = result.scalar_one_or_none()
            
            if not project:
                logger.warning(f"Project {project_code} not found, using default NAS connector")
                return await get_ftp_connector()
            
            # Determine connector type based on connection_type
            if project.connection_type == 'MinIO':
                # Use connection pool for MinIO connections
                from core.minio_pool import get_connection_pool
                pool = await get_connection_pool()
                connector = await pool.get_connector(project_code, project.credentials)
                
                if connector:
                    logger.info(f"Using pooled MinIO connector for project {project_code}")
                    return connector
                else:
                    logger.error(f"Failed to get MinIO connector from pool for project {project_code}")
                    raise HTTPException(status_code=500, detail=f"MinIO connection failed for project {project_code}. Please check MinIO configuration.")
            else:
                # Default to NAS-FTP (no pooling for FTP connections for now)
                from core.nas_connector import get_ftp_connector_from_credentials
                connector = await get_ftp_connector_from_credentials(project.credentials)
                if connector:
                    logger.info(f"Using NAS connector for project {project_code}")
                    return connector
                else:
                    logger.error(f"Failed to create NAS connector for project {project_code}")
                    return await get_ftp_connector()  # Fallback to default
                    
    except Exception as e:
        logger.error(f"Error getting storage connector for project {project_code}: {e}")
        return await get_ftp_connector()  # Fallback to default

@asynccontextmanager
async def get_storage_connector_context(project_code: str = None) -> AsyncGenerator[tuple, None]:
    """Context manager for storage connectors that properly handles connection pooling"""
    connector = await get_storage_connector_for_project(project_code)
    is_minio_pooled = False
    
    # Check if this is a pooled MinIO connection
    if project_code and connector:
        try:
           
            
            async with get_master_db_context() as session:
                result = await session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = result.scalar_one_or_none()
                is_minio_pooled = project and project.connection_type == 'MinIO'
        except Exception:
            pass
    
    try:
        yield connector, is_minio_pooled
    finally:
        # Return MinIO connections to pool
        if is_minio_pooled and connector and project_code:
            try:
                from core.minio_pool import get_connection_pool
                pool = await get_connection_pool()
                await pool.return_connector(project_code, connector)
            except Exception as e:
                logger.warning(f"Failed to return connector to pool: {e}")

async def get_media_from_storage(media_path: str, media_type: str, include_response_time: bool = False, request=None, project_code: str = None):
    """Get media file from storage with optimized streaming for large files"""
    from cache.redis_connector import cache_get, cache_set
    import base64
    
    connector = await get_storage_connector_for_project(project_code)
    if not connector:
        raise HTTPException(status_code=400, detail="Storage connection failed")
    
    try:
        start_time = datetime.now() if include_response_time else None
        
        # Normalize the media path to avoid cache key collisions
        normalized_path = media_path.replace('%20', ' ').strip()
        cache_key = f"media_file:{media_type}:{normalized_path}"
        
        # Get file info first to determine size and handle range requests efficiently
        file_info = None
        try:
            file_info = await connector.get_file_info(media_path)
            if not file_info:
                raise HTTPException(status_code=404, detail=f"{media_type.title()} not found")
            file_size = file_info.get('size', 0)
        except HTTPException:
            raise  # Re-raise HTTP exceptions as-is
        except Exception as e:
            # For MinIO projects, we should be more explicit about failures
            logger.error(f"Failed to get file info for {media_path}: {str(e)}")
            # Try to get file content as fallback to determine size
            try:
                file_content = await connector.get_file_content(media_path)
                if not file_content:
                    raise HTTPException(status_code=404, detail=f"{media_type.title()} not found")
                file_size = len(file_content)
            except Exception as content_error:
                logger.error(f"Failed to get file content for {media_path}: {str(content_error)}")
                raise HTTPException(status_code=404, detail=f"{media_type.title()} not found: {str(content_error)}")
        
        ext = os.path.splitext(media_path)[1].lower()
        
        # Content type mappings
        content_type_maps = {
            "video": {
                '.mp4': 'video/mp4', '.avi': 'video/x-msvideo', '.mov': 'video/quicktime',
                '.wmv': 'video/x-ms-wmv', '.flv': 'video/x-flv', '.webm': 'video/webm',
                '.mkv': 'video/x-matroska', '.m4v': 'video/mp4', '.3gp': 'video/3gpp', '.ogv': 'video/ogg'
            },
            "audio": {
                '.mp3': 'audio/mpeg', '.wav': 'audio/wav', '.flac': 'audio/flac', 
                '.ogg': 'audio/ogg', '.m4a': 'audio/mp4', '.aac': 'audio/aac',
                '.wma': 'audio/x-ms-wma', '.opus': 'audio/opus'
            },
            "text": {
                '.txt': 'text/plain', '.md': 'text/markdown', '.json': 'application/json', 
                '.xml': 'application/xml', '.csv': 'text/csv', '.log': 'text/plain',
                '.yaml': 'text/yaml', '.yml': 'text/yaml'
            },
            "csv": {
                '.csv': 'text/csv', '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                '.xls': 'application/vnd.ms-excel'
            },
            "pdf": {'.pdf': 'application/pdf'}
        }
        
        content_type = content_type_maps.get(media_type, {}).get(ext, 'application/octet-stream')
        
        # Handle range requests for video and audio streaming
        range_header = request.headers.get('Range') if request and media_type in ['video', 'audio'] else None
        
        if range_header:
            # Parse range header (e.g., "bytes=0-1023")
            range_match = range_header.replace('bytes=', '').split('-')
            start = int(range_match[0]) if range_match[0] else 0
            end = int(range_match[1]) if range_match[1] else file_size - 1
            
            # Ensure valid range
            start = max(0, start)
            end = min(file_size - 1, end)
            
            if start >= file_size or end >= file_size or start > end:
                raise HTTPException(status_code=416, detail="Range not satisfiable")
            
            chunk_size = end - start + 1
            
            # Use efficient range request for MinIO/S3-compatible storage
            if hasattr(connector, 'get_file_content_range'):
                logger.info(f"Serving {media_type} range {start}-{end} from MinIO: {normalized_path}")
                media_chunk = await connector.get_file_content_range(media_path, start, end)
            else:
                # Fallback for other connectors: get full content then slice
                logger.info(f"Serving {media_type} range {start}-{end} from NAS (fallback): {normalized_path}")
                if 'file_content' not in locals():
                    file_content = await connector.get_file_content(media_path)
                media_chunk = file_content[start:end + 1]
            
            headers = {
                'Content-Range': f'bytes {start}-{end}/{file_size}',
                'Accept-Ranges': 'bytes',
                'Content-Length': str(chunk_size),
                'Content-Type': content_type,
            }
            
            # Add video-specific headers for better streaming
            if media_type == 'video':
                headers.update({
                    'Cache-Control': 'public, max-age=3600',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Range',
                    'Connection': 'keep-alive',
                })
            
            if include_response_time and start_time:
                headers['X-Response-Time'] = f"{(datetime.now() - start_time).total_seconds():.3f}s"
                headers['X-Source'] = f'Storage-Range-{media_type.title()}'
            
            return Response(
                content=media_chunk,
                status_code=206,  # Partial Content
                headers=headers
            )
        else:
            # Full file request - optimize based on file size
            headers = {
                'Content-Length': str(file_size),
                'Content-Type': content_type,
            }
            
            # Add range support for video and audio
            if media_type in ['video', 'audio']:
                headers.update({
                    'Accept-Ranges': 'bytes',
                    'Cache-Control': 'public, max-age=3600',
                    'Access-Control-Allow-Origin': '*',
                    'Connection': 'keep-alive',
                })
            
            if include_response_time and start_time:
                headers['X-Response-Time'] = f"{(datetime.now() - start_time).total_seconds():.3f}s"
                headers['X-Source'] = f'Storage-Full-{media_type.title()}'
            
            # For large video/audio files, use streaming to avoid memory issues
            if media_type in ['video', 'audio'] and file_size > REDIS_CACHE_THRESHOLD:
                logger.info(f"Streaming large {media_type} file: {normalized_path} ({file_size} bytes)")
                
                # Use streaming response for large files
                if hasattr(connector, 'get_file_stream'):
                    def iter_stream():
                        try:
                            stream = connector.get_file_stream(media_path)
                            chunk_size = 8192  # 8KB chunks
                            while True:
                                chunk = stream.read(chunk_size)
                                if not chunk:
                                    break
                                yield chunk
                        finally:
                            if hasattr(stream, 'close'):
                                stream.close()
                            if hasattr(stream, 'release_conn'):
                                stream.release_conn()
                    
                    return StreamingResponse(
                        iter_stream(), 
                        media_type=content_type,
                        headers=headers
                    )
                else:
                    # Fallback: load full content and stream it
                    file_content = await connector.get_file_content(media_path)
                    def iter_content():
                        yield file_content
                    
                    return StreamingResponse(
                        iter_content(), 
                        media_type=content_type,
                        headers=headers
                    )
            else:
                # Small files or non-streaming media types
                # Try cache first for small files
                cached_content = None
                if file_size <= REDIS_CACHE_THRESHOLD:
                    cached_content = await cache_get(cache_key)
                    if cached_content:
                        logger.info(f"Serving {media_type} from Redis cache: {normalized_path}")
                        # Handle different Redis return types
                        if isinstance(cached_content, str):
                            try:
                                file_content = base64.b64decode(cached_content)
                            except Exception:
                                file_content = cached_content.encode('latin-1') if isinstance(cached_content, str) else cached_content
                        else:
                            file_content = cached_content
                    else:
                        # Load and cache
                        file_content = await connector.get_file_content(media_path)
                        if media_type in ['video', 'audio', 'pdf']:
                            try:
                                cache_data = base64.b64encode(file_content).decode('ascii')
                                await cache_set(cache_key, cache_data, expire_seconds=3600)
                                logger.info(f"Cached {media_type} file in Redis: {normalized_path}")
                            except Exception as e:
                                logger.warning(f"Failed to cache {normalized_path}: {e}")
                        else:
                            await cache_set(cache_key, file_content, expire_seconds=3600)
                else:
                    # Large file, don't cache
                    file_content = await connector.get_file_content(media_path)
                
                # For text, CSV, and PDF, serve directly
                if media_type in ['text', 'csv', 'pdf']:
                    return Response(content=file_content, headers=headers)
                else:
                    # For video and audio, use streaming response
                    def iter_content():
                        yield file_content
                    
                    return StreamingResponse(
                        iter_content(), 
                        media_type=content_type,
                        headers=headers
                    )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving {media_type} {media_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to serve {media_type}: {str(e)}")


async def get_streaming_url_for_media(media_path: str, media_type: str, project_code: str = None, expires_in: int = 3600):
    """Generate a streaming URL for media files, preferring presigned URLs for MinIO/S3"""
    async with get_storage_connector_context(project_code) as (connector, is_minio_pooled):
        if not connector:
            raise HTTPException(status_code=400, detail="Storage connection failed")
        
        try:
            # Check if connector supports presigned URLs (MinIO/S3)
            if hasattr(connector, 'generate_presigned_url'):
                logger.info(f"Generating presigned URL for {media_type}: {media_path}")
                presigned_url = await connector.generate_presigned_url(media_path, expires_in)
                return {
                    "streaming_url": presigned_url,
                    "type": "presigned",
                    "expires_in": expires_in,
                    "supports_range_requests": True
                }
            else:
                # For MinIO projects, presigned URLs should always be supported
                # If not, it indicates a configuration issue
                raise HTTPException(
                    status_code=500, 
                    detail="MinIO connector does not support presigned URLs. Please check MinIO configuration and ensure the connector implements generate_presigned_url method."
                )
                
        except Exception as e:
            logger.error(f"Error generating streaming URL for {media_path}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to generate streaming URL: {str(e)}")

from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, <PERSON>, TIMESTAMP, <PERSON>olean, func, BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from ..project_base import ProjectBase
from ..enums import CaseInsensitiveEnum
from enum import Enum as PyEnum
from sqlalchemy import ForeignKey


class FileType(str, PyEnum):
    """File type enumeration."""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    PDF = "pdf"
    TEXT = "text"
    CSV = "csv"


class ProcessingStatus(str, PyEnum):
    """File processing status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    READY = "ready"
    FAILED = "failed"

class FilesRegistry(ProjectBase):
    """
    Pure file registry without allocation concerns. 
    Manages file metadata, storage, and processing status only.
    """
    __tablename__ = "files_registry"

    # Primary Identity & File Information
    id = Column(Integer, primary_key=True, autoincrement=True, index=True,
                comment="Unique file identifier within this project database")
    batch_id = Column(Integer, ForeignKey("allocation_batches.id", ondelete="CASCADE"), nullable=False, index=True,
                     comment="Reference to allocation batch (project-specific table)")
    file_identifier = Column(String(255), nullable=False, unique=True, index=True,
                            comment="Client-provided file ID or path for external reference and tracking")
    original_filename = Column(String(500), nullable=True,
                              comment="Original filename as uploaded by client (for user display and debugging)")
    file_type = Column(CaseInsensitiveEnum(FileType), nullable=True,
                      comment="Media type determining annotation interface")
    file_extension = Column(String(10), nullable=True,
                           comment="File extension for processing pipeline selection")
    
    # File Storage & Location Management
    storage_location = Column(JSONB, nullable=True,
                             comment="Flexible storage reference (e.g., {'type': 's3', 'bucket': '...', 'path': '...'})")
    file_size_bytes = Column(BigInteger, nullable=True,
                            comment="File size for storage management and processing optimization")
    file_hash = Column(String(64), nullable=True,
                      comment="File hash for integrity verification and duplicate detection")
    
    # Processing Order Management
    sequence_order = Column(Integer, nullable=True,
                           comment="Sequential processing order for ordered workflows")
    
    # Lifecycle Timestamps
    uploaded_at = Column(TIMESTAMP, default=func.now(), nullable=False,
                        comment="When file was initially uploaded to system")

    # Relationships
    file_allocations = relationship("FileAllocations", back_populates="file", cascade="all, delete-orphan")
    batch = relationship("AllocationBatches", back_populates="files")
    def __repr__(self):
        return f"<FilesRegistry(id={self.id}, file_identifier={self.file_identifier}, file_type={self.file_type})>"
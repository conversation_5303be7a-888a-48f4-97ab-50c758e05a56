# AI Model Services Integration Tests

This directory contains comprehensive integration tests for the AI model services in the FastAPI backend application.

## Overview

The test suite covers:
- **AI Processing Service**: End-to-end processing workflows
- **AI Processing Routes**: HTTP API endpoints for AI processing operations
- **AI Registry Routes**: HTTP API endpoints for AI model registry management
- **Cross-service Integration**: Integration between different AI services and components
- **Model Endpoints**: Individual AI service endpoints (OCR, Caption, VQA, Transcription)
- **Database Models**: AI Models Registry and Model Execution Logs
- **Data Factory**: Common test data creation utilities
- **Mock Services**: Simulated AI service responses

## Test Structure

```
tests/
├── __init__.py                        # Package initialization
├── conftest.py                        # Pytest configuration and fixtures
├── test_config.py                     # Test configuration settings
├── requirements-test.txt              # Testing dependencies
├── README.md                          # This documentation
├── common/                            # Common test utilities
│   ├── __init__.py
│   ├── test_factory.py               # Test data factory
│   └── mock_services.py              # Mock AI services
├── test_ai_processing_service.py      # AI Processing Service tests
├── test_ai_processing_routes.py       # AI Processing Routes tests (NEW)
├── test_ai_registry_routes.py         # AI Registry Routes tests (NEW)
├── test_cross_service_integration.py  # Cross-service Integration tests (NEW)
├── test_model_endpoints.py            # Model endpoint tests
├── test_ai_models_registry.py         # AI Models Registry tests
└── test_model_execution_logs.py       # Model Execution Logs tests
```

## Configuration

### Database Configuration

The tests use separate test databases:
- **Master DB**: `master_db_test` (for user management, project registry, AI models registry)
- **Project DB**: `project_db_test` (for file allocations, batches, execution logs)

Default configuration in `test_config.py`:
```python
# Database connection details
db_host: str = "***********"
db_port: int = 5432
db_user: str = "mansi"
db_password: str = "pass123"

# Test database names
master_db_name: str = "master_db_test"
project_db_name: str = "project_db_test"
```

### Environment Variables

You can override configuration using environment variables:
```bash
export TEST_DB_HOST="localhost"
export TEST_DB_PORT="5432"
export TEST_DB_USER="test_user"
export TEST_DB_PASSWORD="test_password"
export TEST_MASTER_DB_NAME="custom_master_test"
export TEST_PROJECT_DB_NAME="custom_project_test"
export TEST_ECHO_SQL="true"
export TEST_MOCK_AI_SERVICES="true"
```

## Setup

### 1. Install Dependencies

```bash
# Install test dependencies
pip install -r tests/requirements-test.txt

# Or install with the main application dependencies
pip install -r requirements.txt
```

### 2. Database Setup

Ensure PostgreSQL is running and accessible with the configured credentials.

The test suite will automatically:
- Create test databases if they don't exist
- Set up database schemas using SQLAlchemy metadata
- Clean up after tests complete

### 3. Validate Environment

```bash
# Validate test environment
python tests/test_config.py
```

## Running Tests

### Run All Tests

```bash
# From the app directory
pytest tests/

# With verbose output
pytest tests/ -v

# With coverage report
pytest tests/ --cov=services --cov=routes --cov-report=html
```

### Run Specific Test Categories

```bash
# AI Processing Service tests
pytest tests/test_ai_processing_service.py -v

# AI Processing Routes tests (NEW)
pytest tests/test_ai_processing_routes.py -v

# AI Registry Routes tests (NEW)
pytest tests/test_ai_registry_routes.py -v

# Cross-service Integration tests (NEW)
pytest tests/test_cross_service_integration.py -v

# Model endpoint tests
pytest tests/test_model_endpoints.py -v

# Database model tests
pytest tests/test_ai_models_registry.py -v
pytest tests/test_model_execution_logs.py -v
```

### Run Specific Test Methods

```bash
# Run a specific test method
pytest tests/test_ai_processing_service.py::TestAIProcessingService::test_process_files_for_project_ocr -v

# Run tests matching a pattern
pytest tests/ -k "test_ocr" -v
```

### Parallel Execution

```bash
# Run tests in parallel (requires pytest-xdist)
pytest tests/ -n auto
```

## New Test Categories (Added)

### AI Processing Routes Tests
- **File**: `test_ai_processing_routes.py`
- **Purpose**: Tests HTTP API endpoints for AI processing operations
- **Coverage**:
  - Batch OCR processing endpoints
  - Batch caption generation endpoints
  - Batch VQA processing endpoints
  - Batch transcription processing endpoints
  - Processing results retrieval
  - File allocation results
  - Project listing
  - Error handling for invalid inputs
  - Authentication requirements

### AI Registry Routes Tests
- **File**: `test_ai_registry_routes.py`
- **Purpose**: Tests HTTP API endpoints for AI model registry management
- **Coverage**:
  - Get all AI models (with filtering)
  - Get specific AI model by ID
  - Create new AI models
  - Update existing AI models
  - Delete AI models
  - Error handling for non-existent models
  - Validation error handling
  - Authentication requirements

### Cross-service Integration Tests
- **File**: `test_cross_service_integration.py`
- **Purpose**: Tests integration between different AI services and components
- **Coverage**:
  - End-to-end AI processing workflows
  - Multi-model processing workflows
  - Model registry and processing service integration
  - Concurrent processing with different models
  - Error handling across services
  - Authentication across services
  - Project workflow integration

## Test Features

### Mock Services

By default, tests use mock AI services to avoid external API dependencies:
- Mock OCR responses with configurable text extraction
- Mock caption generation with customizable captions
- Mock VQA with question-specific answers
- Mock transcription with audio processing simulation

To test with real AI services:
```python
# In test_config.py
mock_ai_services: bool = False
```

### Test Data Factory

The `TestDataFactory` provides convenient methods for creating test data:

```python
# Create a complete test scenario
scenario = await test_data_factory.create_complete_test_scenario(num_files=5)
project = scenario["project"]
batch = scenario["batch"]
files = scenario["files"]
models = scenario["models"]

# Create individual objects
user = await test_data_factory.create_test_user()
ai_model = await test_data_factory.create_test_ai_model("ocr")
batch = await test_data_factory.create_test_batch()

# Cleanup (important!)
await test_data_factory.cleanup_created_data()
```

### Database Fixtures

Pre-configured database fixtures are available:
- `master_db_session`: Master database session
- `project_db_session`: Project database session
- `test_data_factory`: Factory for creating test data

### Service Fixtures

Service instances are available as fixtures:
- `ai_processing_service`: Main AI processing service
- `image_ocr_service`: OCR service endpoint
- `image_caption_service`: Caption service endpoint
- `image_vqa_service`: VQA service endpoint
- `audio_transcription_service`: Transcription service endpoint

## Test Patterns

### Basic Test Structure

```python
@pytest.mark.asyncio
async def test_example(
    ai_processing_service: AIProcessingService,
    test_data_factory: TestDataFactory,
    monkeypatch
):
    # Setup test data
    scenario = await test_data_factory.create_complete_test_scenario(num_files=2)
    
    # Setup mocks
    mock_services = create_mock_services()
    patch_ai_services(monkeypatch, mock_services)
    
    # Execute test
    result = await ai_processing_service.process_files_for_project(
        project_code=scenario["project"].project_code,
        file_identifiers=[f.file_identifier for f in scenario["files"]],
        model_name="test_model",
        processing_type="ocr"
    )
    
    # Verify results
    assert result["status"] == "completed"
    assert len(result["results"]) == 2
    
    # Cleanup
    await test_data_factory.cleanup_created_data()
```

### Error Testing

```python
async def test_error_handling(ai_processing_service, test_data_factory):
    # Test with invalid project
    result = await ai_processing_service.process_files_for_project(
        project_code="INVALID_PROJECT",
        file_identifiers=["test_file.jpg"],
        model_name="test_model",
        processing_type="ocr"
    )
    
    assert result["status"] == "error"
    assert "No files found" in result["message"]
```

## Debugging

### Enable SQL Logging

```python
# In test_config.py
echo_sql: bool = True
```

### View Test Database

```bash
# Connect to test database
psql -h *********** -U mansi -d master_db_test
psql -h *********** -U mansi -d project_db_test
```

### Generate Test Reports

```bash
# HTML coverage report
pytest tests/ --cov=services --cov=routes --cov-report=html

# JSON test report
pytest tests/ --json-report --json-report-file=test_report.json
```

## Troubleshooting

### Database Connection Issues

1. Verify PostgreSQL is running
2. Check database credentials in `test_config.py`
3. Ensure test user has database creation privileges
4. Validate network connectivity to database host

### Import Errors

1. Ensure you're running tests from the correct directory (`fastapi-backend/app/`)
2. Check that all dependencies are installed
3. Verify Python path includes the application modules

### Test Failures

1. Check test database permissions
2. Verify mock services are properly configured
3. Ensure test data cleanup is called in test teardown
4. Check for database schema mismatches

## Contributing

When adding new tests:

1. Follow the existing test patterns
2. Use the `TestDataFactory` for creating test data
3. Always clean up test data in teardown
4. Use appropriate mock services for external dependencies
5. Add comprehensive assertions for both success and error cases
6. Document any new test utilities or patterns

## Performance Considerations

- Tests create and clean up database objects for each test
- Use session-scoped fixtures for expensive setup operations
- Consider using database transactions for faster cleanup
- Mock external services to avoid network latency
- Use parallel test execution for large test suites

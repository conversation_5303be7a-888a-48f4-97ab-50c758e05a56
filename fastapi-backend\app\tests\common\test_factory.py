"""
Test data factory for creating test objects and data.
"""

import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

# Import models
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.users import users, UserRole
from post_db.master_models.clients import Clients
from post_db.master_models.ai_models_registry import AIModelsRegistry, DeploymentStatus
from post_db.allocation_models.files_registry import FilesRegistry
from post_db.allocation_models.allocation_batches import AllocationBatches
from post_db.allocation_models.file_allocations import FileAllocations
from post_db.allocation_models.model_execution_logs import ModelExecutionLogs, ExecutionStatus
from post_db.allocation_models.project_users import ProjectUsers

# Import test configuration
from tests.test_config import get_test_config, get_ai_service_config

class DataFactory:
    """Factory for creating test data objects."""
    
    def __init__(self, master_db_session: AsyncSession, project_db_session: AsyncSession):
        self.master_db = master_db_session
        self.project_db = project_db_session
        self.config = get_test_config()
        self.ai_config = get_ai_service_config()
        
        # Track created objects for cleanup
        self._created_projects = []
        self._created_clients = []
        self._created_users = []
        self._created_models = []
        self._created_batches = []
        self._created_files = []

    async def create_test_client(
        self,
        name: Optional[str] = None,
        **kwargs
    ) -> Clients:
        """Create a test client in master database."""
        if name is None:
            name = f"Test Client {uuid.uuid4().hex[:8].upper()}"

        client = Clients(
            name=name,
            username=kwargs.get("username", f"client_{uuid.uuid4().hex[:8]}"),
            email=kwargs.get("email", f"test_{uuid.uuid4().hex[:8]}@example.com"),
            contact_info=kwargs.get("contact_info", {"phone": "************"}),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        self.master_db.add(client)
        await self.master_db.flush()  # Use flush instead of commit for test transactions
        await self.master_db.refresh(client)

        self._created_clients.append(client)
        return client

    async def create_test_user(
        self, 
        username: Optional[str] = None,
        role: UserRole = UserRole.ANNOTATOR,
        **kwargs
    ) -> Dict[str, Any]:
        """Create a test user in master database."""
        if username is None:
            username = f"test_user_{uuid.uuid4().hex[:8]}"
        
        user_data = {
            "username": username,
            "password_hash": "$2b$12$test_hash",  # Mock password hash
            "full_name": kwargs.get("full_name", f"Test User {username}"),
            "email": kwargs.get("email", f"{username}@test.com"),
            "role": role,
            "is_active": kwargs.get("is_active", True),
            "created_at": datetime.utcnow(),
            "last_login": kwargs.get("last_login"),
        }
        
        # Create user using ORM model
        user = users(
            username=user_data["username"],
            password_hash=user_data["password_hash"],
            full_name=user_data["full_name"],
            email=user_data["email"],
            role=user_data["role"].value,  # Convert enum to string
            is_active=user_data["is_active"],
            created_at=user_data["created_at"],
            last_login=user_data["last_login"]
        )

        self.master_db.add(user)
        await self.master_db.flush()  # Use flush instead of commit for test transactions
        await self.master_db.refresh(user)

        # Convert to dict for return
        user_dict = {
            "id": user.id,
            "username": user.username,
            "password_hash": user.password_hash,
            "full_name": user.full_name,
            "email": user.email,
            "role": user.role,
            "is_active": user.is_active,
            "created_at": user.created_at,
            "last_login": user.last_login
        }

        self._created_users.append(user_dict)
        return user_dict
    
    async def create_test_project(
        self,
        project_code: Optional[str] = None,
        **kwargs
    ) -> ProjectsRegistry:
        """Create a test project in master database."""
        if project_code is None:
            project_code = f"TEST_PROJ_{uuid.uuid4().hex[:8].upper()}"

        # Create a client if client_id is not provided or if client doesn't exist
        client_id = kwargs.get("client_id")
        if client_id is None:
            # Create a new test client
            client = await self.create_test_client()
            client_id = client.id

        project = ProjectsRegistry(
            project_code=project_code,
            project_name=kwargs.get("project_name", f"Test Project {project_code}"),
            project_type=kwargs.get("project_type", "image"),  # Required field - default to 'image'
            client_id=client_id,
            database_name=kwargs.get("database_name", self.config.project_db_name),
            database_host=kwargs.get("database_host", self.config.db_host),
            database_port=kwargs.get("database_port", self.config.db_port),
            folder_path=kwargs.get("folder_path", f"/test/data/{project_code}"),
            project_status=kwargs.get("project_status", "active"),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.master_db.add(project)
        await self.master_db.flush()  # Use flush instead of commit for test transactions
        await self.master_db.refresh(project)
        
        self._created_projects.append(project)
        return project
    
    async def create_test_ai_model(
        self,
        model_type: str = "ocr",
        **kwargs
    ) -> AIModelsRegistry:
        """Create a test AI model in master database."""
        model_config = self.ai_config.test_models.get(model_type, {})
        
        model = AIModelsRegistry(
            model_name=kwargs.get("model_name", model_config.get("model_name", f"Test {model_type.upper()} Model")),
            model_id=kwargs.get("model_id", model_config.get("model_id", f"test_{model_type}_v1")),
            supported_file_types=kwargs.get("supported_file_types", model_config.get("supported_file_types", ["image"])),
            output_format=kwargs.get("output_format", model_config.get("output_format", {"type": "text"})),
            deployment_status=kwargs.get("deployment_status", DeploymentStatus.ACTIVE.value),
            description=kwargs.get("description", f"Test model for {model_type} processing"),
            input_requirements=kwargs.get("input_requirements", {"max_size": "10MB"}),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            created_by=kwargs.get("created_by", "test_system")
        )
        
        self.master_db.add(model)
        await self.master_db.flush()  # Use flush instead of commit for test transactions
        await self.master_db.refresh(model)
        
        self._created_models.append(model)
        return model
    
    async def create_test_batch(
        self,
        batch_identifier: Optional[str] = None,
        **kwargs
    ) -> AllocationBatches:
        """Create a test allocation batch in project database."""
        if batch_identifier is None:
            batch_identifier = f"TEST_BATCH_{uuid.uuid4().hex[:8].upper()}"
        
        batch = AllocationBatches(
            batch_identifier=batch_identifier,
            batch_status=kwargs.get("batch_status", "created"),
            total_files=kwargs.get("total_files", self.config.test_file_count),
            file_list=kwargs.get("file_list", []),
            skill_requirements=kwargs.get("skill_requirements", {}),
            allocation_criteria=kwargs.get("allocation_criteria", {}),
            is_priority=kwargs.get("is_priority", False),
            created_at=datetime.utcnow(),
            deadline=kwargs.get("deadline", datetime.utcnow() + timedelta(days=7)),
            custom_batch_config=kwargs.get("custom_batch_config", {})
        )
        
        self.project_db.add(batch)
        await self.project_db.flush()  # Use flush instead of commit for test transactions
        await self.project_db.refresh(batch)
        
        self._created_batches.append(batch)
        return batch
    
    async def create_test_file(
        self,
        batch_id: int,
        file_identifier: Optional[str] = None,
        **kwargs
    ) -> FilesRegistry:
        """Create a test file in project database."""
        if file_identifier is None:
            file_identifier = f"test_file_{uuid.uuid4().hex[:8]}.jpg"
        
        file_record = FilesRegistry(
            batch_id=batch_id,
            file_identifier=file_identifier,
            original_filename=kwargs.get("original_filename", file_identifier),
            file_type=kwargs.get("file_type", "image"),
            file_extension=kwargs.get("file_extension", ".jpg"),
            storage_location=kwargs.get("storage_location", {"path": f"/test/storage/{file_identifier}"}),
            file_size_bytes=kwargs.get("file_size_bytes", 1024000),  # 1MB
            file_hash=kwargs.get("file_hash", f"hash_{uuid.uuid4().hex}"),
            sequence_order=kwargs.get("sequence_order", 1),
            uploaded_at=datetime.utcnow()
        )
        
        self.project_db.add(file_record)
        await self.project_db.flush()  # Use flush instead of commit for test transactions
        await self.project_db.refresh(file_record)
        
        self._created_files.append(file_record)
        return file_record
    
    async def create_test_model_execution_log(
        self,
        model_name: str,
        file_id: int,
        batch_id: int,
        **kwargs
    ) -> ModelExecutionLogs:
        """Create a test model execution log in project database."""
        execution_log = ModelExecutionLogs(
            model_name=model_name,
            batch_id=batch_id,
            file_id=file_id,
            user_prompt=kwargs.get("user_prompt", "Test prompt"),
            system_prompt=kwargs.get("system_prompt", "Test system prompt"),
            execution_start_time=kwargs.get("execution_start_time", datetime.utcnow()),
            execution_end_time=kwargs.get("execution_end_time"),
            execution_duration_ms=kwargs.get("execution_duration_ms", 1500),
            execution_status=kwargs.get("execution_status", ExecutionStatus.SUCCESS),
            input_data_info=kwargs.get("input_data_info", {"file_size": 1024000}),
            output_data=kwargs.get("output_data", {"result": "test output"}),
            confidence_scores=kwargs.get("confidence_scores", {"overall": 0.95}),
            model_config_snapshot=kwargs.get("model_config_snapshot", {"version": "test_v1"}),
            error_message=kwargs.get("error_message"),
            error_code=kwargs.get("error_code"),
            retry_count=kwargs.get("retry_count", 0),
            triggered_by=kwargs.get("triggered_by", "test_system")
        )
        
        self.project_db.add(execution_log)
        await self.project_db.flush()  # Use flush instead of commit for test transactions
        await self.project_db.refresh(execution_log)
        
        return execution_log

    async def create_test_execution_log(
        self,
        file_id: int,
        model_name: str,
        processing_type: str = "ocr",
        status: str = "completed",
        **kwargs
    ) -> ModelExecutionLogs:
        """Create a test execution log (alias for create_test_model_execution_log)."""
        # Create a batch if not provided
        batch_id = kwargs.get("batch_id")
        if batch_id is None:
            batch = await self.create_test_batch()
            batch_id = batch.id

        return await self.create_test_model_execution_log(
            model_name=model_name,
            file_id=file_id,
            batch_id=batch_id,
            execution_status=ExecutionStatus.SUCCESS if status == "completed" else ExecutionStatus.FAILED,
            output_data=kwargs.get("output_data", {f"{processing_type}_result": "test output"}),
            **kwargs
        )

    async def create_test_file_allocation(
        self,
        file_id: int,
        processing_type: str = "ocr",
        status: str = "completed",
        **kwargs
    ) -> FileAllocations:
        """Create a test file allocation in project database."""
        file_allocation = FileAllocations(
            file_id=file_id,
            allocated_to=kwargs.get("allocated_to", "test_system"),
            allocation_type=kwargs.get("allocation_type", processing_type),
            allocation_status=kwargs.get("allocation_status", status),
            allocated_at=kwargs.get("allocated_at", datetime.utcnow()),
            completed_at=kwargs.get("completed_at", datetime.utcnow() if status == "completed" else None),
            allocation_metadata=kwargs.get("allocation_metadata", {"processing_type": processing_type}),
            priority_level=kwargs.get("priority_level", 1),
            estimated_completion_time=kwargs.get("estimated_completion_time"),
            actual_completion_time=kwargs.get("actual_completion_time")
        )

        self.project_db.add(file_allocation)
        await self.project_db.flush()
        await self.project_db.refresh(file_allocation)

        return file_allocation

    async def create_complete_test_scenario(
        self,
        project_code: Optional[str] = None,
        num_files: int = 5,
        file_type: str = "image"
    ) -> Dict[str, Any]:
        """Create a complete test scenario with project, batch, files, and models."""
        # Create project
        project = await self.create_test_project(project_code=project_code)

        # Create AI models
        ocr_model = await self.create_test_ai_model("ocr")
        caption_model = await self.create_test_ai_model("caption")
        vqa_model = await self.create_test_ai_model("vqa")
        transcription_model = await self.create_test_ai_model("transcription")

        # Create batch
        batch = await self.create_test_batch(total_files=num_files)

        # Create files based on file_type
        files = []
        for i in range(num_files):
            if file_type == "audio":
                file_identifier = f"test_audio_{i+1}.mp3"
                file_extension = ".mp3"
                file_type_param = "audio"
            elif file_type == "video":
                file_identifier = f"test_video_{i+1}.mp4"
                file_extension = ".mp4"
                file_type_param = "video"
            else:  # default to image
                file_identifier = f"test_file_{i+1}.jpg"
                file_extension = ".jpg"
                file_type_param = "image"

            file_record = await self.create_test_file(
                batch_id=batch.id,
                file_identifier=file_identifier,
                file_type=file_type_param,
                file_extension=file_extension
            )
            files.append(file_record)

        # Create test user
        user = await self.create_test_user()

        return {
            "project": project,
            "batch": batch,
            "files": files,
            "models": {
                "ocr": ocr_model,
                "caption": caption_model,
                "vqa": vqa_model,
                "transcription": transcription_model
            },
            "user": user
        }
    
    async def cleanup_created_data(self):
        """Clean up all created test data."""
        # Note: Due to foreign key constraints, we need to delete in reverse order
        
        # Clean up files (will cascade to related records)
        for file_record in self._created_files:
            try:
                await self.project_db.delete(file_record)
            except Exception:
                pass  # May already be deleted by cascade
        
        # Clean up batches
        for batch in self._created_batches:
            try:
                await self.project_db.delete(batch)
            except Exception:
                pass
        
        # Clean up models
        for model in self._created_models:
            try:
                await self.master_db.delete(model)
            except Exception:
                pass
        
        # Clean up projects
        for project in self._created_projects:
            try:
                await self.master_db.delete(project)
            except Exception:
                pass
        
        # Clean up users
        for user in self._created_users:
            try:
                delete_stmt = users.delete().where(users.c.id == user["id"])
                await self.master_db.execute(delete_stmt)
            except Exception:
                pass
        
        # No need to commit in test transactions - they will be rolled back automatically
        # try:
        #     await self.project_db.flush()
        #     await self.master_db.flush()
        # except Exception:
        #     pass
        
        # Clear tracking lists
        self._created_projects.clear()
        self._created_users.clear()
        self._created_models.clear()
        self._created_batches.clear()
        self._created_files.clear()

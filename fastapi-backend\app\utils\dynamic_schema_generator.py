"""
Dynamic Schema Generator for Project Databases.

This module provides utilities for generating database schemas dynamically
based on allocation strategies.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import MetaData, Table, Column, Integer, String, TIMESTAMP, Boolean, Foreign<PERSON>ey, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy import func

from post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType

logger = logging.getLogger(__name__)

class DynamicSchemaGenerator:
    """
    Generates dynamic database schemas based on allocation strategies.
    
    This class creates SQL migration scripts with tables and columns
    customized for specific allocation strategies.
    """
    
    def __init__(self, strategy: AllocationStrategies):
        """
        Initialize the schema generator with an allocation strategy.
        
        Args:
            strategy: The allocation strategy to base the schema on
        """
        self.strategy = strategy
        self.metadata = MetaData()
        
    def get_required_tables(self) -> List[str]:
        """
        Return a list of table names required for this strategy.
        
        Returns:
            List[str]: Names of required tables
        """
        # Base tables always required
        tables = [
            'project_metadata',
            'allocation_batches',
            'files_registry',
            'user_allocations',
            'file_allocations',
            'project_users'
        ]
        
        # Add AI-related tables only if needed
        if self.strategy.requires_ai_preprocessing:
            tables.append('model_execution_logs')
            
        return tables
    
    def generate_migration_script(self) -> str:
        """
        Generate a complete Alembic migration script for the strategy.
        
        Returns:
            str: Complete migration script content
        """
        # Start with the migration template
        migration_content = self._get_migration_template()
        
        # Generate upgrade function content
        upgrade_content = self._generate_upgrade_function()
        
        # Generate downgrade function content
        downgrade_content = self._generate_downgrade_function()
        
        # Replace placeholders in the template
        migration_content = migration_content.replace('{{UPGRADE_CONTENT}}', upgrade_content)
        migration_content = migration_content.replace('{{DOWNGRADE_CONTENT}}', downgrade_content)
        
        return migration_content
    
    def _get_migration_template(self) -> str:
        """
        Return the template for an Alembic migration script.
        
        Returns:
            str: Migration script template
        """
        return '''"""dynamic schema for {{STRATEGY_NAME}}

Revision ID: {{REVISION_ID}}
Revises: 
Create Date: {{CREATE_DATE}}

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '{{REVISION_ID}}'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """Create all project tables based on strategy {{STRATEGY_NAME}}"""
{{UPGRADE_CONTENT}}

def downgrade():
    """Drop all project tables"""
{{DOWNGRADE_CONTENT}}
'''
    
    def _generate_upgrade_function(self) -> str:
        """
        Generate the content of the upgrade function for the migration.
        
        Returns:
            str: Upgrade function content
        """
        upgrade_parts = []
        
        # Add standard tables
        upgrade_parts.append(self._generate_project_metadata_table())
        upgrade_parts.append(self._generate_project_users_table())
        upgrade_parts.append(self._generate_allocation_batches_table())
        upgrade_parts.append(self._generate_files_registry_table())
        upgrade_parts.append(self._generate_user_allocations_table())
        upgrade_parts.append(self._generate_file_allocations_table())
        
        # Add AI-related tables if needed
        if self.strategy.requires_ai_preprocessing:
            upgrade_parts.append(self._generate_model_execution_logs_table())
        
        return '\n\n'.join(upgrade_parts)
    
    def _generate_downgrade_function(self) -> str:
        """
        Generate the content of the downgrade function for the migration.
        
        Returns:
            str: Downgrade function content
        """
        tables = self.get_required_tables()
        tables.reverse()  # Reverse for proper drop order
        
        drop_statements = []
        for table in tables:
            drop_statements.append(f"    op.drop_table('{table}')")
        
        return '\n'.join(drop_statements)
    
    def _generate_project_metadata_table(self) -> str:
        """Generate SQL for creating the project_metadata table."""
        return '''    # Create project_metadata table
    op.create_table('project_metadata',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('project_code', sa.String(length=50), nullable=False),
        sa.Column('master_db_project_id', sa.Integer(), nullable=False),
        sa.Column('annotation_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('validation_rules', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('allocation_strategy', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('credentials', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('connection_type', sa.String(length=50), nullable=True),
        sa.Column('folder_path', sa.String(length=500), nullable=True),
        sa.Column('instructions', sa.Text(), nullable=True),
        sa.Column('batch_size', sa.Integer(), nullable=True),
        sa.Column('supported_file_types', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('file_processing_pipeline', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('quality_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False),
        sa.Column('last_sync_with_master', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(), default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )'''

    def _generate_project_users_table(self) -> str:
        """Generate SQL for creating the project_users table with dynamic roles based on strategy."""
        return '''    # Create project_users table
    op.create_table('project_users',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=255), nullable=False),
        sa.Column('role', sa.String(length=255), nullable=False),
        sa.Column('current_batch', sa.Integer(), nullable=True),
        sa.Column('completed_batches', postgresql.ARRAY(sa.Integer()), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'username', name='_user_id_username_uc')
    )'''

    def _generate_allocation_batches_table(self) -> str:
        """Generate SQL for creating the allocation_batches table with dynamic columns based on strategy."""
        # Start with base columns
        columns = [
            "        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),",
            "        sa.Column('batch_identifier', sa.String(length=100), nullable=False),",
            "        sa.Column('batch_status', sa.String(length=50), default='created', nullable=False),",
            "        sa.Column('total_files', sa.Integer(), nullable=False),",
            "        sa.Column('file_list', postgresql.JSONB(astext_type=sa.Text()), nullable=True),",
            "        sa.Column('skill_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True),",
            "        sa.Column('allocation_criteria', postgresql.JSONB(astext_type=sa.Text()), nullable=True),",
            "        sa.Column('is_priority', sa.Boolean(), default=False, nullable=False),",
            "        sa.Column('created_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),",
            "        sa.Column('deadline', sa.TIMESTAMP(), nullable=True),",
            f"        sa.Column('annotation_count', sa.Integer(), default=0, nullable=False),",
        ]
        
        # Add assignment and completion count columns
        columns.append(f"        sa.Column('assignment_count', sa.Integer(), default=0, nullable=False, comment='Number of files assigned in this batch'),")
        columns.append(f"        sa.Column('completion_count', sa.Integer(), default=0, nullable=False, comment='Number of files completed in this batch'),")
        
        # Add dynamic annotator columns based on strategy - INTEGER type with foreign keys to user_id
        # Note: We're using Integer type without FK constraint since user_id is not unique in project_users
        for i in range(1, self.strategy.num_annotators + 1):
            columns.append(f"        sa.Column('annotator_{i}', sa.Integer(), nullable=True, comment='References user_id in project_users table'),")
        
        # Add verifier columns for strategies that require verification
        if self.strategy.requires_verification:
            columns.append("        sa.Column('verifier', sa.Integer(), nullable=True, comment='References user_id in project_users table'),")
        
        # Add custom config column at the end
        columns.append("        sa.Column('custom_batch_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True),")
        
        # Add constraints
        columns.append("        sa.PrimaryKeyConstraint('id'),")
        columns.append("        sa.UniqueConstraint('batch_identifier', name='_batch_identifier_uc')")
        
        # Build the complete table creation statement
        table_sql = f"""    # Create allocation_batches table with dynamic columns for {self.strategy.strategy_name}
    op.create_table('allocation_batches',
{chr(10).join(columns)}
    )"""
        
        return table_sql

    def _generate_files_registry_table(self) -> str:
        """Generate SQL for creating the files_registry table."""
        return '''    # Create files_registry table
    op.create_table('files_registry',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('batch_id', sa.Integer(), nullable=False),
        sa.Column('file_identifier', sa.String(length=255), nullable=False),
        sa.Column('original_filename', sa.String(length=500), nullable=True),
        sa.Column('file_type', sa.String(length=50), nullable=True),
        sa.Column('file_extension', sa.String(length=10), nullable=True),
        sa.Column('storage_location', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('file_size_bytes', sa.BigInteger(), nullable=True),
        sa.Column('file_hash', sa.String(length=64), nullable=True),
        sa.Column('sequence_order', sa.Integer(), nullable=True),
        sa.Column('uploaded_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['batch_id'], ['allocation_batches.id'], ondelete='CASCADE')
    )'''

    def _generate_user_allocations_table(self) -> str:
        """Generate SQL for creating the user_allocations table."""
        return '''    # Create user_allocations table
    op.create_table('user_allocations',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('batch_id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=255), nullable=False),
        sa.Column('files_completed', sa.Integer(), default=0, nullable=False),
        sa.Column('total_files', sa.Integer(), nullable=True),
        sa.Column('completed_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('allocation_role', sa.String(length=50), default='annotator', nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False),
        sa.Column('allocated_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('activation_deadline', sa.TIMESTAMP(), nullable=True),
        sa.Column('completion_deadline', sa.TIMESTAMP(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['project_users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['batch_id'], ['allocation_batches.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('batch_id', 'username', name='_batch_username_uc')
    )'''

    def _generate_file_allocations_table(self) -> str:
        """Generate SQL for creating the file_allocations table with dynamic columns based on strategy."""
        # Start with base columns
        columns = [
            "        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),",
            "        sa.Column('file_id', sa.Integer(), nullable=False),",
            "        sa.Column('batch_id', sa.Integer(), nullable=False),",
            "        sa.Column('allocation_sequence', sa.Integer(), default=1, nullable=False),",
            "        sa.Column('workflow_phase', sa.String(length=50), default='annotation', nullable=False),",
            "        sa.Column('processing_status', sa.String(length=50), default='pending', nullable=False),",
            "        sa.Column('processed_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),",
            "        sa.Column('preprocessing_results', postgresql.JSONB(astext_type=sa.Text()), nullable=True),",
            "        sa.Column('allocated_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),",
            "        sa.Column('activation_deadline', sa.TIMESTAMP(), nullable=True),",
            "        sa.Column('completion_deadline', sa.TIMESTAMP(), nullable=True),",
            "        sa.Column('assignment_count', sa.Integer(), default=0, nullable=False),",
            "        sa.Column('completion_count', sa.Integer(), default=0, nullable=False),",
        ]
        
        # Add dynamic annotator columns based on strategy - INTEGER type with foreign keys to user_id
        # Note: We're using Integer type without FK constraint since user_id is not unique in project_users
        for i in range(1, self.strategy.num_annotators + 1):
            columns.append(f"        sa.Column('annotator_{i}', sa.Integer(), nullable=True, comment='References user_id in project_users table'),")
            columns.append(f"        sa.Column('annotator_{i}_review', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Review data for annotator {i}'),")
        
        # Add verifier columns for strategies that require verification
        if self.strategy.requires_verification:
            columns.append("        sa.Column('verifier', sa.Integer(), nullable=True, comment='References user_id in project_users table'),")
            columns.append("        sa.Column('verifier_review', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Review data for verifier'),")
        
        # Add allocation_rules column at the end
        columns.append("        sa.Column('allocation_rules', postgresql.JSONB(astext_type=sa.Text()), nullable=True),")
        
        # Add constraints
        columns.append("        sa.PrimaryKeyConstraint('id'),")
        columns.append("        sa.ForeignKeyConstraint(['file_id'], ['files_registry.id'], ondelete='CASCADE'),")
        columns.append("        sa.ForeignKeyConstraint(['batch_id'], ['allocation_batches.id'], ondelete='CASCADE'),")
        columns.append("        sa.UniqueConstraint('file_id', 'allocation_sequence', name='_file_allocation_seq_uc')")
        
        # Build the complete table creation statement
        table_sql = f"""    # Create file_allocations table with dynamic columns for {self.strategy.strategy_name}
    op.create_table('file_allocations',
{chr(10).join(columns)}
    )"""
        
        return table_sql







    def _generate_model_execution_logs_table(self) -> str:
        """Generate SQL for creating the model_execution_logs table (only if AI preprocessing is required)."""
        return '''    # Create model_execution_logs table (for AI preprocessing)
    op.create_table('model_execution_logs',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('model_name', sa.String(length=255), nullable=False),
        sa.Column('batch_id', sa.Integer(), nullable=True),
        sa.Column('file_id', sa.Integer(), nullable=True),
        sa.Column('user_prompt', sa.Text(), nullable=True),
        sa.Column('system_prompt', sa.Text(), nullable=True),
        sa.Column('execution_start_time', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('execution_end_time', sa.TIMESTAMP(), nullable=True),
        sa.Column('execution_duration_ms', sa.Integer(), nullable=True),
        sa.Column('execution_status', sa.String(length=50), nullable=False),
        sa.Column('input_data_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('output_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('confidence_scores', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('model_config_snapshot', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_code', sa.String(length=50), nullable=True),
        sa.Column('retry_count', sa.Integer(), default=0, nullable=False),
        sa.Column('triggered_by', sa.String(length=255), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['batch_id'], ['allocation_batches.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['file_id'], ['files_registry.id'], ondelete='CASCADE')
    )'''

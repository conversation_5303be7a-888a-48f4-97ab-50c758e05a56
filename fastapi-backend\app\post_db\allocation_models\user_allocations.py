from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, TIMESTAMP, Boolean, func, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from ..project_base import ProjectBase
from ..enums import CaseInsensitiveEnum
from enum import Enum as PyEnum

class AllocationRole(str, PyEnum):
    """Allocation role enumeration."""
    ANNOTATOR = "annotator"
    REVIEWER = "reviewer"
    AUDITOR = "auditor"
    VERIFIER = "verifier"

class UserAllocations(ProjectBase):
    """
    Manages allocation of users to batches. 
    Tracks allocation status and capacity, separate from annotation work tracking.
    """
    __tablename__ = "user_allocations"
    
    __table_args__ = (
        UniqueConstraint('batch_id', 'username', name='_batch_username_uc'),
    )

    # Primary Identity & Relationships
    id = Column(Integer, primary_key=True, autoincrement=True, index=True,
                comment="Unique allocation identifier within this project database")
    user_id = Column(Integer, ForeignKey("project_users.id", ondelete="CASCADE"), nullable=True, index=True,
                    comment="Reference to project_users.id (primary key)")
    batch_id = Column(Integer, ForeignKey("allocation_batches.id", ondelete="CASCADE"), nullable=False, index=True,
                     comment="Reference to allocation batch (project-specific table)")
    username = Column(String(255), nullable=False, index=True,
                     comment="Username for quick reference")
    
    # Additional fields for current simple workflow (from UserAllocationsSimple)
    files_completed = Column(Integer, default=0, nullable=False,
                           comment="Number of files user has completed in this batch")
    total_files = Column(Integer, nullable=True,
                        comment="Total files in this batch")
    completed_at = Column(TIMESTAMP, nullable=True,
                         comment="When user completed the batch")
    
    # Allocation Configuration
    allocation_role = Column(CaseInsensitiveEnum(AllocationRole), default=AllocationRole.ANNOTATOR, nullable=True,
                            comment="User's role for this allocation")
    
    # Allocation Status & Timeline
    is_active = Column(Boolean, default=True, nullable=False,
                      comment="Whether this allocation is currently active")
    allocated_at = Column(TIMESTAMP, default=func.now(), nullable=False,
                         comment="When user was allocated to this batch")
    activation_deadline = Column(TIMESTAMP, nullable=True,
                                comment="Deadline for user to start working")
    completion_deadline = Column(TIMESTAMP, nullable=True,
                                comment="Deadline for completing allocated work")

    batch = relationship("AllocationBatches", back_populates="user_allocations")
    user = relationship("ProjectUsers", back_populates="allocations")
    def __repr__(self):
        return f"<UserAllocations(id={self.id}, batch_id={self.batch_id}, username={self.username}, allocation_role={self.allocation_role}, is_active={self.is_active})>"
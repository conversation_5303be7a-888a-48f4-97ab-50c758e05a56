import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from core.session_manager import get_project_db_session
from post_db.allocation_models.files_registry import FilesRegistry
from post_db.allocation_models.file_allocations import FileAllocations, ProcessingStatus as AllocationProcessingStatus
from post_db.allocation_models.model_execution_logs import ModelExecutionLogs, ExecutionStatus
from routes.ai_processing.storage_utils import get_ai_storage_connection
logger = logging.getLogger(__name__)


class AIProcessingService:
    """Complete AI Processing Service with Database Storage Integration"""
    
    def __init__(self):
        pass
        
    async def process_files_for_project(
        self,
        project_code: str,
        file_identifiers: List[str],
        model_name: str,
        processing_type: str,  # 'transcription', 'ocr', 'caption', 'vqa'
        user_prompt: Optional[str] = None,
        system_prompt: Optional[str] = None,
        **processing_params
    ) -> Dict[str, Any]:
        try:
            logger.info(f"Starting AI processing: {project_code} | {len(file_identifiers)} files | {processing_type} | {model_name}")
            
            # Validate file identifiers are provided
            if not file_identifiers:
                return {
                    "status": "error", 
                    "message": "File identifiers are required for AI processing",
                    "project_code": project_code
                }

            # Get files from files_registry 
            files_to_process = await self._get_files_from_registry(project_code, file_identifiers)
            if not files_to_process:
                return {
                    "status": "error",
                    "message": "No files found for processing", 
                    "project_code": project_code
                }
            
            # Process each file
            processing_results: List[Dict[str, Any]] = []
            for file_data in files_to_process:
                result = await self._process_single_file(
                    project_code=project_code,
                    file_data=file_data,
                    model_name=model_name,
                    processing_type=processing_type,
                    user_prompt=user_prompt,
                    system_prompt=system_prompt,
                    **processing_params
                )
                processing_results.append(result)
            
            return {
                "status": "completed",
                "project_code": project_code,
                "processing_type": processing_type,
                "model_name": model_name,
                "total_files": len(files_to_process),
                "results": processing_results,
                "processed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in AI processing workflow for project {project_code}: {e}")
            return {
                "status": "error",
                "message": str(e),
                "project_code": project_code
            }
    
    
    async def _get_files_from_registry(self, project_code: str, file_identifiers: List[str]) -> List[Dict[str, Any]]:
        """Get files from files_registry with optimized storage connection reuse"""
        async with get_project_db_session(project_code) as db:
            stmt = select(FilesRegistry).where(
                FilesRegistry.file_identifier.in_(file_identifiers)
            )
            result = await db.execute(stmt)
            files = result.scalars().all()
            
            if not files:
                return []

            # Get storage connection for this project
            storage_info = await get_ai_storage_connection(project_code)
            
            file_list = []
            for file in files:
                file_data = {
                    "id": file.id,
                    "file_identifier": file.file_identifier,
                    "batch_id": file.batch_id,
                    "original_filename": file.original_filename,
                    "file_type": str(file.file_type) if file.file_type else None,
                    "file_extension": file.file_extension,
                    "file_size_bytes": file.file_size_bytes,
                    "file_hash": file.file_hash,
                    "storage_location": file.storage_location,
                    "storage_info": storage_info
                }
                
                file_list.append(file_data)

            return file_list
    
    
    
    async def _process_single_file(
        self,
        project_code: str,
        file_data: Dict[str, Any],
        model_name: str,
        processing_type: str,
        user_prompt: Optional[str],
        system_prompt: Optional[str],
        **processing_params
    ) -> Dict[str, Any]:
        """Process a single file with AI model"""
        
        execution_start = datetime.utcnow()
        
        # Handle VQA which requires a question
        if processing_type == "vqa":
            question = processing_params.get('question') or user_prompt or ''
            if not question:
                return {
                    "file_identifier": file_data.get('file_identifier', ''),
                    "status": "failed",
                    "error": "Question is required for VQA processing"
                }
            ai_result = await self._process_ai_service_call(
                file_data, model_name, processing_type, question, question=question, **processing_params
            )
        else:
            # Handle transcription, ocr, caption
            ai_result = await self._process_ai_service_call(
                file_data, model_name, processing_type, user_prompt, **processing_params
            )
        
        execution_end = datetime.utcnow()
        execution_duration = int((execution_end - execution_start).total_seconds() * 1000)
        
        # Prepare processed metadata
        processed_metadata = {
            "file_identifier": file_data.get('file_identifier', ''),
            "original_filename": file_data.get('original_filename', ''),
            "file_type": file_data.get('file_type', ''),
            "file_size_bytes": file_data.get('file_size_bytes', 0),
            "processing_type": processing_type,
            "model_name": model_name,
            "processed_at": execution_end.isoformat()
        }
        
        # Prepare preprocessing results
        preprocessing_results = {
            "prompt": user_prompt,
            "system_prompt": system_prompt,
            "model_response": ai_result,
            "execution_duration_ms": execution_duration,
            "processing_params": processing_params
        }
        
        # Log successful execution if we have database access
        if file_data.get('id') is not None:
            await self._log_model_execution(
                project_code=project_code,
                file_id=file_data.get('id'),
                batch_id=file_data.get('batch_id', 1),
                model_name=model_name,
                execution_status=ExecutionStatus.SUCCESS,
                user_prompt=user_prompt,
                system_prompt=system_prompt,
                execution_start_time=execution_start,
                execution_end_time=execution_end,
                execution_duration_ms=execution_duration,
                output_data=ai_result,
                input_data_info={
                    "file_size_bytes": file_data.get('file_size_bytes', 0),
                    "file_type": file_data.get('file_type', ''),
                    "file_extension": file_data.get('file_extension', '')
                }
            )
            
            # Update file allocation
            await self._update_file_allocation(
                project_code=project_code,
                file_id=file_data.get('id'),
                processed_metadata=processed_metadata,
                preprocessing_results=preprocessing_results
            )
        
        return {
            "file_identifier": file_data.get('file_identifier', ''),
            "status": "success",
            "result": ai_result,
            "processed_metadata": processed_metadata,
            "execution_duration_ms": execution_duration
        }
    
    async def _process_ai_service_call(
        self, 
        file_data: Dict[str, Any], 
        model_name: str, 
        processing_type: str, 
        prompt: Optional[str] = None,
        **additional_params
    ) -> Dict[str, Any]:
        """Centralized method for all AI processing service calls using direct storage access"""
        filename = file_data.get('original_filename', 'unknown')
        
        try:
            # Get storage content directly from file_data
            storage_info = file_data.get('storage_info')
            if not storage_info:
                raise ValueError(f"Storage info required")
            
            file_path = file_data.get('file_identifier')
            if not file_path:
                raise ValueError("File identifier required")
            
            connector = storage_info['connector']
            file_content = await connector.get_file_content(file_path)
            
            if not file_content:
                raise ValueError(f"Failed to retrieve content: {file_path}")
            
            # Create simple UploadFile-like object for AI services
            class SimpleUploadFile:
                def __init__(self, content: bytes, filename: str):
                    self.filename = filename
                    self.content_type = 'image/jpeg'
                    self._content = content
                
                async def read(self, size: int = -1) -> bytes:
                    return self._content
                
                async def seek(self, position: int = 0) -> None:
                    pass
                
                @property
                def file(self):
                    return self
            
            upload_file = SimpleUploadFile(file_content, filename)
            
            # Call appropriate AI service based on processing type
            if processing_type == "transcription":
                from routes.model_endpoints.audio_transcription import AudioTranscriptionService
                service = AudioTranscriptionService()
                
                result = await service.transcribe_audio(upload_file, model_name, prompt)
                
            elif processing_type == "ocr":
                from routes.model_endpoints.image_ocr import ImageOCRService
                service = ImageOCRService()
                result = await service.extract_text(upload_file, model_name, prompt)   
            elif processing_type == "caption":
                from routes.model_endpoints.image_caption import get_image_caption_service
                service = get_image_caption_service()
                result = await service.generate_caption(upload_file, model_name, prompt)
            elif processing_type == "vqa":
                from routes.model_endpoints.image_vqa import ImageVQAService
                service = ImageVQAService()
                question = additional_params.get('question') or prompt or ''
                if not question:
                    raise ValueError("Question is required for VQA processing")
                result = await service.answer_question(upload_file, question, model_name)  
            else:
                raise ValueError(f"Unsupported processing type: {processing_type}")
            return result
            
        except Exception as e:
            logger.error(f"Error in {processing_type} processing: {str(e)}")
            return {
                "file_name": filename,
                "status": "failed", 
                "error": str(e)
            }

    
    async def _log_model_execution(
        self,
        project_code: str,
        file_id: int,
        batch_id: int,
        model_name: str,
        execution_status: ExecutionStatus,
        user_prompt: Optional[str] = None,
        system_prompt: Optional[str] = None,
        execution_start_time: Optional[datetime] = None,
        execution_end_time: Optional[datetime] = None,
        execution_duration_ms: Optional[int] = None,
        output_data: Optional[Dict] = None,
        input_data_info: Optional[Dict] = None,
        error_message: Optional[str] = None,
        error_code: Optional[str] = None
    ):
        """Log model execution in model_execution_logs"""
        async with get_project_db_session(project_code) as db:
            log_entry = ModelExecutionLogs(
                model_name=model_name,
                batch_id=batch_id,
                file_id=file_id,
                user_prompt=user_prompt,
                system_prompt=system_prompt,
                execution_start_time=execution_start_time or datetime.utcnow(),
                execution_end_time=execution_end_time,
                execution_duration_ms=execution_duration_ms,
                execution_status=execution_status,
                input_data_info=input_data_info,
                output_data=output_data,
                error_message=error_message,
                error_code=error_code,
                triggered_by="ai_processing_service"
            )
            
            db.add(log_entry)
            await db.commit()
    
    async def _update_file_allocation(
        self,
        project_code: str,
        file_id: int,
        processed_metadata: Dict[str, Any],
        preprocessing_results: Dict[str, Any]
    ):
        """Update file allocation with processed metadata and preprocessing results"""
        try:
            async with get_project_db_session(project_code) as db:
                # Find file allocation for this file
                stmt = select(FileAllocations).where(FileAllocations.file_id == file_id)
                result = await db.execute(stmt)
                allocation = result.scalar_one_or_none()
                
                if allocation:
                    # Update existing allocation
                    allocation.processed_metadata = processed_metadata
                    allocation.preprocessing_results = preprocessing_results
                    allocation.processing_status = AllocationProcessingStatus.READY
                    # Set processing type and model name if not already set
                    if not hasattr(allocation, 'processing_type') or not allocation.processing_type:
                        setattr(allocation, 'processing_type', processed_metadata.get('processing_type'))
                    if not hasattr(allocation, 'model_name') or not allocation.model_name:
                        setattr(allocation, 'model_name', processed_metadata.get('model_name'))
                    if not hasattr(allocation, 'processed_at') or not allocation.processed_at:
                        setattr(allocation, 'processed_at', datetime.fromisoformat(processed_metadata.get('processed_at')))
                else:
                    # Get batch_id from file registry if not available in preprocessing_results
                    batch_id = preprocessing_results.get('batch_id')
                    if not batch_id:
                        file_stmt = select(FilesRegistry).where(FilesRegistry.id == file_id)
                        file_result = await db.execute(file_stmt)
                        file = file_result.scalar_one_or_none()
                        if file:
                            batch_id = file.batch_id
                        else:
                            # Critical error: File not found in registry
                            logger.error(f"File {file_id} not found in files_registry. Cannot create allocation without valid batch_id.")
                            raise ValueError(f"File {file_id} not found in files_registry. Allocation cannot be created without valid batch_id.") 
                    
                    # Create new allocation
                    allocation = FileAllocations(
                        file_id=file_id,
                        batch_id=batch_id,
                        processed_metadata=processed_metadata,
                        preprocessing_results=preprocessing_results,
                        processing_status=AllocationProcessingStatus.READY
                    )
                    # Set processing type and model name for new allocations
                    if hasattr(allocation, 'processing_type'):
                        setattr(allocation, 'processing_type', processed_metadata.get('processing_type'))
                    if hasattr(allocation, 'model_name'):
                        setattr(allocation, 'model_name', processed_metadata.get('model_name'))
                    if hasattr(allocation, 'processed_at'):
                        setattr(allocation, 'processed_at', datetime.fromisoformat(processed_metadata.get('processed_at')))
                    db.add(allocation)
                
                await db.commit()
                
        except Exception as e:
            raise ValueError(f"Failed to update file allocation: {str(e)}")

# Singleton instance
ai_processing_service = AIProcessingService()

def get_ai_processing_service() -> AIProcessingService:
    """Dependency injection for AI Processing Service"""
    return ai_processing_service
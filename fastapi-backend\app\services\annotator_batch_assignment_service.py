"""
Service for assigning annotators to batches.
Handles the business logic for batch assignment when annotators click "Start Annotating".
"""

import logging
from typing import Dict, Any, Optional

from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies
from post_db.master_models.users import users as Users
from repositories.batch_assignment_repository import BatchAssignmentRepository
from sqlalchemy import select

logger = logging.getLogger(__name__)

class AnnotatorBatchAssignmentService:
    """
    Service for managing annotator batch assignments.
    """
    
    def __init__(self):
        self.repository = BatchAssignmentRepository()
    
    async def get_user_active_project(self, user_id: int) -> Optional[str]:
        """
        Get the active project code for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Optional[str]: Active project code or None if no active project
        """
        try:
            async with get_master_db_context() as session:
                result = await session.execute(
                    select(Users.active_project)
                    .where(Users.id == user_id)
                )
                active_project = result.scalar_one_or_none()
                return active_project
        except Exception as e:
            logger.error(f"Error getting user active project: {str(e)}")
            return None
    
    async def get_project_allocation_strategy(self, project_code: str) -> Optional[AllocationStrategies]:
        """
        Get the allocation strategy for a project.
        
        Args:
            project_code: Project code
            
        Returns:
            Optional[AllocationStrategies]: The project's allocation strategy, or None if not found
        """
        try:
            async with get_master_db_context() as session:
                # Get project info
                result = await session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = result.scalar_one_or_none()
                
                if not project or not project.allocation_strategy_id:
                    return None
                
                # Get strategy info
                strategy_result = await session.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                )
                strategy = strategy_result.scalar_one_or_none()
                
                return strategy
                
        except Exception as e:
            logger.error(f"Error getting project allocation strategy: {str(e)}")
            return None
    
    async def assign_annotator_to_next_batch(self, user_id: int) -> Dict[str, Any]:
        """
        Assign an annotator to the next available batch.
        
        This is the main method called when an annotator clicks "Start Annotating".
        
        Args:
            user_id: User ID of the annotator
            
        Returns:
            Dict: Result with success status, batch details, and files
        """
        try:
            logger.info(f"Starting batch assignment for user {user_id}")
            
            # 1. Get user's active project
            project_code = await self.get_user_active_project(user_id)
            logger.info(f"User's active project: {project_code}")
            if not project_code:
                logger.error(f"No active project for user {user_id}")
                return {
                    "success": False,
                    "error": "User has no active project assigned",
                    "error_code": "NO_ACTIVE_PROJECT"
                }
            
            # 2. Check if user already has a current batch
            current_batch = await self.repository.get_user_current_batch(project_code, user_id)
            logger.info(f"User's current batch: {current_batch}")
            if current_batch is not None:
                # User already has an active batch, return that batch's details
                batch_details = await self.repository.get_batch_with_files(project_code, current_batch)
                if batch_details:
                    return {
                        "success": True,
                        "message": "User already has an active batch",
                        "batch": batch_details,
                        "is_existing_batch": True
                    }
                else:
                    return {
                        "success": False,
                        "error": "User has current batch but batch details not found",
                        "error_code": "BATCH_NOT_FOUND"
                    }
            
            # 3. Get project allocation strategy
            strategy = await self.get_project_allocation_strategy(project_code)
            if not strategy:
                return {
                    "success": False,
                    "error": "Project has no allocation strategy configured",
                    "error_code": "NO_ALLOCATION_STRATEGY"
                }
            
            # 4. Get user info from project database
            user_info = await self.repository.get_user_project_info(project_code, user_id)
            if not user_info:
                return {
                    "success": False,
                    "error": "User not found in project database",
                    "error_code": "USER_NOT_IN_PROJECT"
                }
            
            # 5. Find available batches (assignment_count < annotation_count)
            available_batches = await self.repository.get_available_batches(project_code)
            
            if not available_batches:
                return {
                    "success": False,
                    "error": "No available batches found. All batches are fully allocated.",
                    "error_code": "NO_AVAILABLE_BATCHES"
                }
            
            # 5.1. Filter out batches that the user has already completed
            completed_batches = user_info.get("completed_batches", [])
            logger.info(f"User {user_id} completed batches: {completed_batches}")
            
            eligible_batches = []
            skipped_count = 0
            
            for batch in available_batches:
                if batch.id not in completed_batches:
                    eligible_batches.append(batch)
                else:
                    skipped_count += 1
            
            if not eligible_batches:
                return {
                    "success": False,
                    "error": "No eligible batches found. User has completed all available batches.",
                    "error_code": "ALL_BATCHES_COMPLETED"
                }
            
            logger.info(f"Found {len(eligible_batches)} eligible batches, skipped {skipped_count} completed batches (total: {len(available_batches)})")
            
            # 6. Find the first batch that can accommodate this user
            for batch in eligible_batches:
                # Find next available annotator slot
                next_slot = await self.repository.find_next_annotator_slot(project_code, batch.id)
                
                if next_slot is not None:
                    # Found an available slot, assign the user
                    assignment_success = await self.repository.assign_user_to_batch(
                        project_code=project_code,
                        user_id=user_id,
                        username=user_info["username"],
                        batch_id=batch.id,
                        annotator_slot=next_slot,
                        total_files=batch.total_files
                    )
                    
                    if assignment_success:
                        # Get the updated batch details with files
                        batch_details = await self.repository.get_batch_with_files(project_code, batch.id)
                        
                        return {
                            "success": True,
                            "message": f"Successfully assigned to batch {batch.batch_identifier}",
                            "batch": batch_details,
                            "assigned_slot": next_slot,
                            "is_existing_batch": False,
                            "project_code": project_code,
                            "strategy": {
                                "name": strategy.strategy_name,
                                "type": strategy.strategy_type,
                                "num_annotators": strategy.num_annotators
                            }
                        }
                    else:
                        return {
                            "success": False,
                            "error": "Failed to assign user to batch due to database error",
                            "error_code": "ASSIGNMENT_FAILED"
                        }
            
            # 7. No available slots found in any batch
            return {
                "success": False,
                "error": "All batches are fully allocated. No available slots found.",
                "error_code": "ALL_BATCHES_FULL"
            }
            
        except Exception as e:
            logger.error(f"Error assigning annotator to batch: {str(e)}")
            return {
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "error_code": "INTERNAL_ERROR"
            }
    
    async def get_user_batch_status(self, user_id: int) -> Dict[str, Any]:
        """
        Get the current batch status for a user.
        Used to determine if "Start Annotating" button should be enabled/disabled.
        
        Args:
            user_id: User ID
            
        Returns:
            Dict: User batch status information
        """
        try:
            # Get user's active project
            project_code = await self.get_user_active_project(user_id)
            if not project_code:
                return {
                    "success": False,
                    "has_active_batch": False,
                    "can_start_annotating": False,
                    "error": "No active project assigned"
                }
            
            # Check if user has a current batch
            current_batch = await self.repository.get_user_current_batch(project_code, user_id)
            
            if current_batch is not None:
                # User has an active batch
                batch_details = await self.repository.get_batch_with_files(project_code, current_batch)
                return {
                    "success": True,
                    "has_active_batch": True,
                    "can_start_annotating": True,  # Can continue with existing batch
                    "current_batch": batch_details,
                    "project_code": project_code
                }
            else:
                # User has no active batch, check if there are available batches
                strategy = await self.get_project_allocation_strategy(project_code)
                if not strategy:
                    return {
                        "success": False,
                        "has_active_batch": False,
                        "can_start_annotating": False,
                        "error": "No allocation strategy configured"
                    }
                
                available_batches = await self.repository.get_available_batches(project_code)
                
                # Get user info to check completed batches
                user_info = await self.repository.get_user_project_info(project_code, user_id)
                if user_info:
                    completed_batches = user_info.get("completed_batches", [])
                    # Filter out completed batches
                    eligible_batches = [
                        batch for batch in available_batches 
                        if batch.id not in completed_batches
                    ]
                else:
                    eligible_batches = available_batches
                
                return {
                    "success": True,
                    "has_active_batch": False,
                    "can_start_annotating": len(eligible_batches) > 0,
                    "available_batches_count": len(available_batches),
                    "eligible_batches_count": len(eligible_batches),
                    "project_code": project_code
                }
                
        except Exception as e:
            logger.error(f"Error getting user batch status: {str(e)}")
            return {
                "success": False,
                "has_active_batch": False,
                "can_start_annotating": False,
                "error": f"Internal server error: {str(e)}"
            }

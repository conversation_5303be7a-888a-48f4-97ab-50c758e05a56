from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import select, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime

from dependencies.auth import get_current_active_user
from post_db.allocation_models.file_allocations import FileAllocations
from post_db.allocation_models.files_registry import FilesRegistry
from post_db.allocation_models.model_execution_logs import ModelExecutionLogs
from services.ai_processing_service import get_ai_processing_service, AIProcessingService
from core.session_manager import get_project_db_session

router = APIRouter()

@router.get("/results")
async def get_processing_results(
    project_code: Optional[str] = Query(None, description="Project code to fetch results for"),
    processing_type: Optional[str] = Query(None, description="Filter by processing type (caption, ocr, vqa, transcription)"),
    limit: int = Query(100, ge=1, le=500, description="Limit the number of results (max 500)"),
    offset: int = Query(0, ge=0, description="Offset for pagination (must be non-negative)"),
    current_user: dict = Depends(get_current_active_user),
    ai_service: AIProcessingService = Depends(get_ai_processing_service)
):
    """
    Get AI processing results from the database for a specific project.
    Results are sorted by most recent first.
    """
    try:
        # Validate parameters
        if limit > 500:
            limit = 500  # Enforce maximum limit
        
        if offset < 0:
            offset = 0  # Ensure offset is non-negative
            
        # If no project code provided, return empty results
        if not project_code:
            return {
                "status": "success",
                "message": "Please specify a project_code to fetch results",
                "count": 0,
                "results": []
            }
            
        # Get database session for the project
        async with get_project_db_session(project_code) as db:
            # First, build a base query for counting total results
            base_query = select(
                ModelExecutionLogs,
                FilesRegistry.file_identifier,
                FilesRegistry.original_filename
            ).join(
                FilesRegistry,
                ModelExecutionLogs.file_id == FilesRegistry.id
            )
            
            # Apply processing type filter if provided
            if processing_type:
                # Extract processing type from output_data JSON
                base_query = base_query.filter(ModelExecutionLogs.model_name.ilike(f"%{processing_type}%"))
            
            # Count total results
            count_query = select(func.count()).select_from(base_query.subquery())
            count_result = await db.execute(count_query)
            total_count = count_result.scalar() or 0
            
            # Now build the paginated query
            query = base_query.order_by(desc(ModelExecutionLogs.execution_start_time))
            
            # Apply pagination
            query = query.limit(limit).offset(offset)
            
            # Execute the query
            result = await db.execute(query)
            rows = result.all()
            
            # Format the results
            processing_results = []
            for row in rows:
                log, file_identifier, original_filename = row
                
                # Get file allocation data if available
                allocation_query = select(FileAllocations).where(
                    FileAllocations.file_id == log.file_id
                )
                allocation_result = await db.execute(allocation_query)
                allocation = allocation_result.scalar_one_or_none()
                
                processed_metadata = {}
                preprocessing_results = {}
                
                if allocation:
                    processed_metadata = allocation.processed_metadata or {}
                    preprocessing_results = allocation.preprocessing_results or {}
                
                # Format the result
                processing_results.append({
                    "id": log.id,
                    "file_id": log.file_id,
                    "file_identifier": file_identifier,
                    "original_filename": original_filename,
                    "batch_id": log.batch_id,
                    "model_name": log.model_name,
                    "processing_type": processing_type or extract_processing_type(log.model_name),
                    "processed_at": log.execution_end_time.isoformat() if log.execution_end_time else log.execution_start_time.isoformat(),
                    "status": log.execution_status,
                    "result": log.output_data,
                    "processed_metadata": processed_metadata,
                    "execution_duration_ms": log.execution_duration_ms,
                    "project_code": project_code
                })
            
            return {
                "status": "success",
                "project_code": project_code,
                "count": total_count,
                "displayed": len(processing_results),
                "results": processing_results
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching processing results: {str(e)}"
        )

@router.get("/results/{result_id}")
async def get_processing_result_by_id(
    result_id: int,
    project_code: str = Query(..., description="Project code the result belongs to"),
    current_user: dict = Depends(get_current_active_user),
    ai_service: AIProcessingService = Depends(get_ai_processing_service)
):
    """
    Get a specific AI processing result by its ID.
    """
    try:
        # Get database session for the project
        async with get_project_db_session(project_code) as db:
            # Query for the specific model execution log
            query = select(
                ModelExecutionLogs,
                FilesRegistry.file_identifier,
                FilesRegistry.original_filename
            ).join(
                FilesRegistry,
                ModelExecutionLogs.file_id == FilesRegistry.id
            ).where(
                ModelExecutionLogs.id == result_id
            )
            
            # Execute the query
            result = await db.execute(query)
            row = result.one_or_none()
            
            if not row:
                raise HTTPException(
                    status_code=404,
                    detail=f"Processing result with ID {result_id} not found"
                )
            
            log, file_identifier, original_filename = row
            
            # Get file allocation data if available
            allocation_query = select(FileAllocations).where(
                FileAllocations.file_id == log.file_id
            )
            allocation_result = await db.execute(allocation_query)
            allocation = allocation_result.scalar_one_or_none()
            
            processed_metadata = {}
            preprocessing_results = {}
            
            if allocation:
                processed_metadata = allocation.processed_metadata or {}
                preprocessing_results = allocation.preprocessing_results or {}
            
            # Format the result
            processing_result = {
                "id": log.id,
                "file_id": log.file_id,
                "file_identifier": file_identifier,
                "original_filename": original_filename,
                "batch_id": log.batch_id,
                "model_name": log.model_name,
                "processing_type": extract_processing_type(log.model_name),
                "processed_at": log.execution_end_time.isoformat() if log.execution_end_time else log.execution_start_time.isoformat(),
                "status": log.execution_status,
                "result": log.output_data,
                "processed_metadata": processed_metadata,
                "preprocessing_results": preprocessing_results,
                "execution_duration_ms": log.execution_duration_ms,
                "project_code": project_code
            }
            
            return processing_result
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching processing result: {str(e)}"
        )

@router.get("/results/file/{file_id}")
async def get_processing_results_by_file_id(
    file_id: int,
    project_code: str = Query(..., description="Project code the file belongs to"),
    current_user: dict = Depends(get_current_active_user),
    ai_service: AIProcessingService = Depends(get_ai_processing_service)
):
    """
    Get all AI processing results for a specific file.
    """
    try:
        # Get database session for the project
        async with get_project_db_session(project_code) as db:
            # Query for the file's model execution logs
            query = select(
                ModelExecutionLogs,
                FilesRegistry.file_identifier,
                FilesRegistry.original_filename
            ).join(
                FilesRegistry,
                ModelExecutionLogs.file_id == FilesRegistry.id
            ).where(
                ModelExecutionLogs.file_id == file_id
            ).order_by(
                desc(ModelExecutionLogs.execution_start_time)
            )
            
            # Execute the query
            result = await db.execute(query)
            rows = result.all()
            
            if not rows:
                return {
                    "status": "success",
                    "project_code": project_code,
                    "file_id": file_id,
                    "count": 0,
                    "results": []
                }
            
            # Format the results
            processing_results = []
            for row in rows:
                log, file_identifier, original_filename = row
                
                # Format the result
                processing_results.append({
                    "id": log.id,
                    "file_id": log.file_id,
                    "file_identifier": file_identifier,
                    "original_filename": original_filename,
                    "batch_id": log.batch_id,
                    "model_name": log.model_name,
                    "processing_type": extract_processing_type(log.model_name),
                    "processed_at": log.execution_end_time.isoformat() if log.execution_end_time else log.execution_start_time.isoformat(),
                    "status": log.execution_status,
                    "result": log.output_data,
                    "execution_duration_ms": log.execution_duration_ms,
                    "project_code": project_code
                })
            
            return {
                "status": "success",
                "project_code": project_code,
                "file_id": file_id,
                "count": len(processing_results),
                "results": processing_results
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching file processing results: {str(e)}"
        )

def extract_processing_type(model_name: str) -> str:
    """
    Extract the processing type from the model name.
    """
    model_name = model_name.lower()
    if "caption" in model_name:
        return "caption"
    elif "ocr" in model_name:
        return "ocr"
    elif "vqa" in model_name:
        return "vqa"
    elif "transcription" in model_name or "whisper" in model_name:
        return "transcription"
    else:
        return "unknown"

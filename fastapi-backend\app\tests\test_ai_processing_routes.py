"""
Integration tests for AI Processing Routes.
Tests the HTTP API endpoints for AI processing operations.
"""

import pytest
import json
from typing import Dict, Any, List
from unittest.mock import patch, AsyncMock, Mock
from fastapi.testclient import TestClient
from fastapi import status
from httpx import AsyncClient

# Import will be done within test functions to avoid circular imports
from tests.common.mock_services import create_mock_services, patch_ai_services, patch_storage_connector
from tests.common.test_factory import DataFactory


@pytest.mark.asyncio
class TestAIProcessingRoutes:
    """Test cases for AI Processing Routes integration."""
    
    @pytest.fixture
    async def test_client(self):
        """Create test client for the FastAPI app."""
        from main import app
        async with Async<PERSON>lient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    async def authenticated_headers(self, test_data_factory: DataFactory):
        """Create authenticated headers for API requests."""
        # Create test user
        user = await test_data_factory.create_test_user(
            username="test_ai_user",
            role="ADMIN"
        )
        
        # Mock authentication token
        with patch('dependencies.auth.verify_token') as mock_verify:
            mock_verify.return_value = {
                "sub": user.username,
                "token_type": "access"
            }
            
            return {
                "Authorization": "Bearer test_token",
                "Content-Type": "application/json"
            }
    
    async def test_batch_ocr_project_folder(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test batch OCR processing for project folder."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=3)
        project = scenario["project"]
        
        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])
        
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}
            
            # Prepare form data
            form_data = {
                "project_code": project.project_code,
                "model_name": "test_ocr_v1",
                "custom_prompt": "Extract all text from images"
            }
            
            # Make API request
            response = await test_client.post(
                f"/api/v1/ai/ocr/batch/project",
                data=form_data,
                headers=authenticated_headers
            )
            
            # Verify response
            assert response.status_code == status.HTTP_200_OK
            result = response.json()
            
            assert result["status"] == "completed"
            assert result["project_code"] == project.project_code
            assert result["processing_type"] == "ocr"
            assert result["model_name"] == "test_ocr_v1"
            assert "total_files" in result
            assert "results" in result
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_batch_caption_project_folder(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test batch caption generation for project folder."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=2)
        project = scenario["project"]
        
        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])
        
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}
            
            # Prepare form data
            form_data = {
                "project_code": project.project_code,
                "model_name": "test_caption_v1",
                "custom_prompt": "Generate detailed captions"
            }
            
            # Make API request
            response = await test_client.post(
                f"/api/v1/ai/caption/batch/project",
                data=form_data,
                headers=authenticated_headers
            )
            
            # Verify response
            assert response.status_code == status.HTTP_200_OK
            result = response.json()
            
            assert result["status"] == "completed"
            assert result["project_code"] == project.project_code
            assert result["processing_type"] == "caption"
            assert result["model_name"] == "test_caption_v1"
            assert "total_files" in result
            assert "results" in result
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_batch_vqa_project_folder(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test batch VQA processing for project folder."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=2)
        project = scenario["project"]
        
        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])
        
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}
            
            # Prepare form data
            form_data = {
                "project_code": project.project_code,
                "model_name": "test_vqa_v1",
                "question": "What objects are visible in this image?"
            }
            
            # Make API request
            response = await test_client.post(
                f"/api/v1/ai/vqa/batch/project",
                data=form_data,
                headers=authenticated_headers
            )
            
            # Verify response
            assert response.status_code == status.HTTP_200_OK
            result = response.json()
            
            assert result["status"] == "completed"
            assert result["project_code"] == project.project_code
            assert result["processing_type"] == "vqa"
            assert result["model_name"] == "test_vqa_v1"
            assert "total_files" in result
            assert "results" in result
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_batch_transcription_project_folder(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test batch transcription processing for project folder."""
        # Setup test data with audio files
        scenario = await test_data_factory.create_complete_test_scenario(
            num_files=2,
            file_type="audio"
        )
        project = scenario["project"]
        
        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])
        
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}
            
            # Prepare form data
            form_data = {
                "project_code": project.project_code,
                "model_name": "test_transcription_v1",
                "custom_prompt": "Transcribe audio accurately"
            }
            
            # Make API request
            response = await test_client.post(
                f"/api/v1/ai/transcription/batch/project",
                data=form_data,
                headers=authenticated_headers
            )
            
            # Verify response
            assert response.status_code == status.HTTP_200_OK
            result = response.json()
            
            assert result["status"] == "completed"
            assert result["project_code"] == project.project_code
            assert result["processing_type"] == "transcription"
            assert result["model_name"] == "test_transcription_v1"
            assert "total_files" in result
            assert "results" in result
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_get_processing_results(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test getting processing results."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=2)
        project = scenario["project"]
        
        # Create some execution logs
        for file in scenario["files"]:
            await test_data_factory.create_test_execution_log(
                file_id=file.id,
                model_name="test_ocr_v1",
                processing_type="ocr",
                status="completed"
            )
        
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}
            
            # Make API request
            response = await test_client.get(
                f"/api/v1/ai/processing/results",
                params={
                    "project_code": project.project_code,
                    "processing_type": "ocr",
                    "limit": 10,
                    "offset": 0
                },
                headers=authenticated_headers
            )
            
            # Verify response
            assert response.status_code == status.HTTP_200_OK
            result = response.json()
            
            assert result["status"] == "success"
            assert "count" in result
            assert "results" in result
            assert isinstance(result["results"], list)
        
        # Cleanup
        await test_data_factory.cleanup_created_data()

    async def test_get_file_allocation_results(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test getting file allocation results."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=3)
        project = scenario["project"]

        # Create some file allocations
        for file in scenario["files"]:
            await test_data_factory.create_test_file_allocation(
                file_id=file.id,
                processing_type="ocr",
                status="completed"
            )

        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}

            # Make API request
            response = await test_client.get(
                f"/api/v1/ai/file-allocations",
                params={
                    "project_code": project.project_code,
                    "processing_type": "ocr",
                    "limit": 10,
                    "offset": 0
                },
                headers=authenticated_headers
            )

            # Verify response
            assert response.status_code == status.HTTP_200_OK
            result = response.json()

            assert result["status"] == "success"
            assert "count" in result
            assert "results" in result
            assert isinstance(result["results"], list)

        # Cleanup
        await test_data_factory.cleanup_created_data()

    async def test_get_projects_list(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test getting projects list."""
        # Setup test data
        project1 = await test_data_factory.create_test_project(
            project_name="Test Project 1",
            project_code="TEST001"
        )
        project2 = await test_data_factory.create_test_project(
            project_name="Test Project 2",
            project_code="TEST002"
        )

        # Mock authentication (optional for this endpoint)
        with patch('routes.ai_processing.project_folders.get_optional_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}

            # Make API request
            response = await test_client.get(
                f"/api/v1/ai/projects",
                headers=authenticated_headers
            )

            # Verify response
            assert response.status_code == status.HTTP_200_OK
            result = response.json()

            assert result["status"] == "success"
            assert "projects" in result
            assert len(result["projects"]) >= 2

            # Verify project data structure
            project_codes = [p["project_code"] for p in result["projects"]]
            assert "TEST001" in project_codes
            assert "TEST002" in project_codes

        # Cleanup
        await test_data_factory.cleanup_created_data()

    async def test_invalid_project_code_error(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test error handling for invalid project code."""
        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)

        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}

            # Prepare form data with invalid project code
            form_data = {
                "project_code": "INVALID_PROJECT",
                "model_name": "test_ocr_v1",
                "custom_prompt": "Extract text"
            }

            # Make API request
            response = await test_client.post(
                f"/api/v1/ai/ocr/batch/project",
                data=form_data,
                headers=authenticated_headers
            )

            # Verify error response
            assert response.status_code == status.HTTP_404_NOT_FOUND
            result = response.json()
            assert "not found" in result["detail"].lower()

    async def test_missing_required_parameters(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str]
    ):
        """Test error handling for missing required parameters."""
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}

            # Make API request without required parameters
            response = await test_client.post(
                f"/api/v1/ai/ocr/batch/project",
                data={},  # Empty form data
                headers=authenticated_headers
            )

            # Verify error response
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_vqa_missing_question_error(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test VQA error handling when question is missing."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=1)
        project = scenario["project"]

        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)

        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_ai_user"}

            # Prepare form data without question
            form_data = {
                "project_code": project.project_code,
                "model_name": "test_vqa_v1"
                # Missing required 'question' parameter
            }

            # Make API request
            response = await test_client.post(
                f"/api/v1/ai/vqa/batch/project",
                data=form_data,
                headers=authenticated_headers
            )

            # Verify error response
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Cleanup
        await test_data_factory.cleanup_created_data()

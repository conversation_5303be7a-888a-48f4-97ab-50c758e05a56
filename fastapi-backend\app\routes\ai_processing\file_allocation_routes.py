from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import select, desc, func
from typing import List, Optional, Dict, Any
from datetime import datetime

from dependencies.auth import get_current_active_user
from post_db.allocation_models.file_allocations import FileAllocations
from post_db.allocation_models.files_registry import FilesRegistry
from services.ai_processing_service import get_ai_processing_service, AIProcessingService
from core.session_manager import get_project_db_session

router = APIRouter()

@router.get("/file-allocations")
async def get_file_allocation_results(
    project_code: Optional[str] = Query(None, description="Project code to fetch results for"),
    processing_type: Optional[str] = Query(None, description="Filter by processing type (caption, ocr, vqa, transcription)"),
    limit: int = Query(100, ge=1, le=500, description="Limit the number of results (max 500)"),
    offset: int = Query(0, ge=0, description="Offset for pagination (must be non-negative)"),
    # current_user: dict = Depends(get_current_active_user),  # Temporarily disabled for testing
    ai_service: AIProcessingService = Depends(get_ai_processing_service)
):
    try:
        # Validate parameters
        if limit > 500:
            limit = 500  # Enforce maximum limit
        
        if offset < 0:
            offset = 0  # Ensure offset is non-negative
            
        # If no project code provided, return empty results
        if not project_code:
            return {
                "status": "success",
                "message": "Please specify a project_code to fetch results",
                "count": 0,
                "results": []
            }
            
        # Get database session for the project
        async with get_project_db_session(project_code) as db:
            try:
                # Try to query file_allocations first
                base_query = select(
                    FileAllocations,
                    FilesRegistry.file_identifier,
                    FilesRegistry.original_filename
                ).join(
                    FilesRegistry,
                    FileAllocations.file_id == FilesRegistry.id
                )
                
                # Apply processing type filter if provided
                if processing_type:
                    # Check if the column exists and use it
                    try:
                        base_query = base_query.filter(FileAllocations.processing_type == processing_type)
                    except:
                        pass  # Ignore if column doesn't exist
                
                # Count total results
                count_query = select(func.count()).select_from(base_query.subquery())
                count_result = await db.execute(count_query)
                total_count = count_result.scalar() or 0
                
                # Build the paginated query
                query = base_query.order_by(desc(FileAllocations.allocated_at))
                
                # Apply pagination
                query = query.limit(limit).offset(offset)
                
                # Execute the query
                result = await db.execute(query)
                rows = result.all()
                
                # Format the results
                allocation_results = []
                for row in rows:
                    allocation, file_identifier, original_filename = row
                    
                    # Format the result with safe attribute access
                    allocation_results.append({
                        "id": allocation.id,
                        "file_id": allocation.file_id,
                        "file_identifier": file_identifier,
                        "original_filename": original_filename,
                        "project_code": project_code,
                        "processing_type": getattr(allocation, 'processing_type', 'unknown'),
                        "processing_status": getattr(allocation, 'processing_status', 'pending'),
                        "model_name": getattr(allocation, 'model_name', None),
                        "processed_at": getattr(allocation, 'allocated_at', datetime.now()).isoformat(),
                        "preprocessing_results": getattr(allocation, 'preprocessing_results', {}) or {},
                        "processed_metadata": getattr(allocation, 'processed_metadata', {}) or {},
                    })
                
                return {
                    "status": "success",
                    "project_code": project_code,
                    "count": total_count,
                    "displayed": len(allocation_results),
                    "results": allocation_results
                }
                
            except Exception as fa_error:
                # If file_allocations query fails, fall back to model_execution_logs
                from post_db.allocation_models.model_execution_logs import ModelExecutionLogs
                
                # Build query using model_execution_logs instead
                base_query = select(
                    ModelExecutionLogs,
                    FilesRegistry.file_identifier,
                    FilesRegistry.original_filename
                ).join(
                    FilesRegistry,
                    ModelExecutionLogs.file_id == FilesRegistry.id
                )
                
                # Apply processing type filter if provided
                if processing_type:
                    base_query = base_query.filter(ModelExecutionLogs.model_name.ilike(f"%{processing_type}%"))
                
                # Count total results
                count_query = select(func.count()).select_from(base_query.subquery())
                count_result = await db.execute(count_query)
                total_count = count_result.scalar() or 0
                
                # Build the paginated query
                query = base_query.order_by(desc(ModelExecutionLogs.execution_start_time))
                
                # Apply pagination
                query = query.limit(limit).offset(offset)
                
                # Execute the query
                result = await db.execute(query)
                rows = result.all()
                
                # Format the results from model execution logs
                def extract_processing_type(model_name: str) -> str:
                    model_name = model_name.lower()
                    if "caption" in model_name:
                        return "caption"
                    elif "ocr" in model_name:
                        return "ocr"
                    elif "vqa" in model_name:
                        return "vqa"
                    elif "transcription" in model_name or "whisper" in model_name:
                        return "transcription"
                    else:
                        return "unknown"
                
                allocation_results = []
                for row in rows:
                    log, file_identifier, original_filename = row
                    
                    # Format the result using model execution log data
                    allocation_results.append({
                        "id": log.id,
                        "file_id": log.file_id,
                        "file_identifier": file_identifier,
                        "original_filename": original_filename,
                        "project_code": project_code,
                        "processing_type": extract_processing_type(log.model_name),
                        "processing_status": log.execution_status,
                        "model_name": log.model_name,
                        "processed_at": log.execution_end_time.isoformat() if log.execution_end_time else log.execution_start_time.isoformat(),
                        "preprocessing_results": log.output_data or {},
                        "processed_metadata": {},
                    })
                
                return {
                    "status": "success",
                    "project_code": project_code,
                    "count": total_count,
                    "displayed": len(allocation_results),
                    "results": allocation_results,
                    "note": "Data retrieved from model_execution_logs as fallback"
                }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching file allocation results: {str(e)}"
        )

@router.get("/file-allocations/{allocation_id}")
async def get_file_allocation_by_id(
    allocation_id: int,
    project_code: str = Query(..., description="Project code the allocation belongs to"),
    # current_user: dict = Depends(get_current_active_user),  # Temporarily disabled for testing
    ai_service: AIProcessingService = Depends(get_ai_processing_service)
):
    """
    Get a specific file allocation by its ID.
    """
    try:
        # Get database session for the project
        async with get_project_db_session(project_code) as db:
            # Query for the specific file allocation
            query = select(
                FileAllocations,
                FilesRegistry.file_identifier,
                FilesRegistry.original_filename
            ).join(
                FilesRegistry,
                FileAllocations.file_id == FilesRegistry.id
            ).where(
                FileAllocations.id == allocation_id
            )
            
            # Execute the query
            result = await db.execute(query)
            row = result.one_or_none()
            
            if not row:
                raise HTTPException(
                    status_code=404,
                    detail=f"File allocation with ID {allocation_id} not found"
                )
            
            allocation, file_identifier, original_filename = row
            
            # Format the result
            allocation_result = {
                "id": allocation.id,
                "file_id": allocation.file_id,
                "file_identifier": file_identifier,
                "original_filename": original_filename,
                "project_code": project_code,
                "processing_type": allocation.processing_type,
                "processing_status": allocation.processing_status,
                "model_name": allocation.model_name,
                "processed_at": allocation.processed_at.isoformat() if allocation.processed_at else None,
                "preprocessing_results": allocation.preprocessing_results or {},
                "processed_metadata": allocation.processed_metadata or {},
            }
            
            return allocation_result
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching file allocation: {str(e)}"
        )

@router.get("/file-allocations/file/{file_id}")
async def get_file_allocations_by_file_id(
    file_id: int,
    project_code: str = Query(..., description="Project code the file belongs to"),
    current_user: dict = Depends(get_current_active_user),
    ai_service: AIProcessingService = Depends(get_ai_processing_service)
):
    """
    Get all file allocations for a specific file.
    """
    try:
        # Get database session for the project
        async with get_project_db_session(project_code) as db:
            # Query for the file's allocations
            query = select(
                FileAllocations,
                FilesRegistry.file_identifier,
                FilesRegistry.original_filename
            ).join(
                FilesRegistry,
                FileAllocations.file_id == FilesRegistry.id
            ).where(
                FileAllocations.file_id == file_id
            ).order_by(
                desc(FileAllocations.allocated_at)
            )
            
            # Execute the query
            result = await db.execute(query)
            rows = result.all()
            
            if not rows:
                return {
                    "status": "success",
                    "project_code": project_code,
                    "file_id": file_id,
                    "count": 0,
                    "results": []
                }
            
            # Format the results
            allocation_results = []
            for row in rows:
                allocation, file_identifier, original_filename = row
                
                # Format the result
                allocation_results.append({
                    "id": allocation.id,
                    "file_id": allocation.file_id,
                    "file_identifier": file_identifier,
                    "original_filename": original_filename,
                    "project_code": project_code,
                    "processing_type": getattr(allocation, 'processing_type', None),
                    "processing_status": getattr(allocation, 'processing_status', None),
                    "model_name": getattr(allocation, 'model_name', None),
                    "processed_at": allocation.processed_at.isoformat() if hasattr(allocation, 'processed_at') and allocation.processed_at else None,
                    "preprocessing_results": getattr(allocation, 'preprocessing_results', {}) or {},
                    "processed_metadata": getattr(allocation, 'processed_metadata', {}) or {},
                })
            
            return {
                "status": "success",
                "project_code": project_code,
                "file_id": file_id,
                "count": len(allocation_results),
                "results": allocation_results
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching file allocations: {str(e)}"
        )

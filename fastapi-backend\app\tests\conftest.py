"""
Pytest configuration and fixtures for AI model services integration tests.
"""

import pytest
import asyncio
import os
import logging
from typing import AsyncGenerator, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import text, select
import asyncpg
from datetime import datetime

# Import test configuration
from tests.test_config import TestDatabaseConfig, get_test_config
from tests.common.test_factory import DataFactory

# Import database models and utilities
from post_db.base import Base
from post_db.project_base import ProjectBase
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.users import users
from post_db.master_models.ai_models_registry import AIModelsRegistry, DeploymentStatus
from post_db.allocation_models.files_registry import FilesRegistry
from post_db.allocation_models.allocation_batches import AllocationBatches
from post_db.allocation_models.model_execution_logs import ModelExecutionLogs

# Services will be imported within fixtures to avoid circular imports

logger = logging.getLogger(__name__)

# Configure pytest-asyncio
pytest_plugins = ('pytest_asyncio',)

# Import pytest_asyncio for fixture decorators
import pytest_asyncio

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
def test_config():
    """Get test configuration."""
    return get_test_config()

@pytest_asyncio.fixture(scope="session")
async def master_db_engine(test_config):
    """Create master database engine for testing."""
    engine = create_async_engine(
        test_config.master_db_url,
        echo=test_config.echo_sql,
        pool_size=5,
        max_overflow=10
    )
    yield engine
    await engine.dispose()

@pytest_asyncio.fixture(scope="session")
async def project_db_engine(test_config):
    """Create project database engine for testing."""
    engine = create_async_engine(
        test_config.project_db_url,
        echo=test_config.echo_sql,
        pool_size=5,
        max_overflow=10
    )
    yield engine
    await engine.dispose()

@pytest_asyncio.fixture(scope="session")
async def setup_test_databases(test_config):
    """Setup test databases with clean schema."""
    # Create test databases if they don't exist
    await _create_test_database(test_config.master_db_name, test_config)
    await _create_test_database(test_config.project_db_name, test_config)
    
    # Create master database tables
    master_engine = create_async_engine(test_config.master_db_url, echo=False)
    async with master_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    await master_engine.dispose()
    
    # Create project database tables
    project_engine = create_async_engine(test_config.project_db_url, echo=False)
    async with project_engine.begin() as conn:
        await conn.run_sync(ProjectBase.metadata.create_all)
    await project_engine.dispose()
    
    yield
    
    # Cleanup after all tests
    await _cleanup_test_database(test_config.master_db_name, test_config)
    await _cleanup_test_database(test_config.project_db_name, test_config)

@pytest_asyncio.fixture
async def master_db_session(master_db_engine, setup_test_databases) -> AsyncGenerator[AsyncSession, None]:
    """Create master database session for testing."""
    async_session = async_sessionmaker(
        master_db_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autoflush=False,
        autocommit=False
    )

    async with async_session() as session:
        # Start a transaction
        transaction = await session.begin()
        try:
            yield session
        finally:
            # Check if transaction is still active before rolling back
            try:
                if transaction.is_active:
                    await transaction.rollback()
            except Exception:
                # Transaction may already be closed, ignore cleanup errors
                pass
            # Session will be closed automatically by the context manager

@pytest_asyncio.fixture
async def project_db_session(project_db_engine, setup_test_databases) -> AsyncGenerator[AsyncSession, None]:
    """Create project database session for testing."""
    async_session = async_sessionmaker(
        project_db_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autoflush=False,
        autocommit=False
    )

    async with async_session() as session:
        # Start a transaction
        transaction = await session.begin()
        try:
            yield session
        finally:
            # Check if transaction is still active before rolling back
            try:
                if transaction.is_active:
                    await transaction.rollback()
            except Exception:
                # Transaction may already be closed, ignore cleanup errors
                pass
            # Session will be closed automatically by the context manager

@pytest.fixture
def test_data_factory(master_db_session, project_db_session):
    """Create test data factory."""
    return DataFactory(master_db_session, project_db_session)

@pytest.fixture
def ai_processing_service():
    """Create AI processing service instance."""
    try:
        from services.ai_processing_service import AIProcessingService
        return AIProcessingService()
    except ImportError as e:
        pytest.skip(f"Could not import AIProcessingService: {e}")

@pytest.fixture
def image_ocr_service():
    """Create Image OCR service instance."""
    try:
        from routes.model_endpoints.image_ocr import ImageOCRService
        return ImageOCRService()
    except ImportError as e:
        pytest.skip(f"Could not import ImageOCRService: {e}")

@pytest.fixture
def image_caption_service():
    """Create Image Caption service instance."""
    try:
        from routes.model_endpoints.image_caption import ImageCaptionService
        return ImageCaptionService()
    except ImportError as e:
        pytest.skip(f"Could not import ImageCaptionService: {e}")

@pytest.fixture
def image_vqa_service():
    """Create Image VQA service instance."""
    try:
        from routes.model_endpoints.image_vqa import ImageVQAService
        return ImageVQAService()
    except ImportError as e:
        pytest.skip(f"Could not import ImageVQAService: {e}")

@pytest.fixture
def audio_transcription_service():
    """Create Audio Transcription service instance."""
    try:
        from routes.model_endpoints.audio_transcription import AudioTranscriptionService
        return AudioTranscriptionService()
    except ImportError as e:
        pytest.skip(f"Could not import AudioTranscriptionService: {e}")

@pytest_asyncio.fixture
async def test_client():
    """Create test client for the FastAPI app."""
    try:
        from httpx import AsyncClient
        from main import app
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    except ImportError as e:
        pytest.skip(f"Could not import required modules for test client: {e}")

# Helper functions
async def _create_test_database(db_name: str, config: TestDatabaseConfig):
    """Create test database if it doesn't exist."""
    conn = None
    try:
        # Connect to postgres database
        conn = await asyncpg.connect(
            host=config.db_host,
            port=config.db_port,
            user=config.db_admin_user,
            password=config.db_admin_password,
            database="postgres"
        )
        
        # Check if database exists
        exists = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1", db_name
        )
        
        if not exists:
            await conn.execute(f'CREATE DATABASE "{db_name}"')
            await conn.execute(f'GRANT ALL PRIVILEGES ON DATABASE "{db_name}" TO {config.db_user}')
            logger.info(f"Created test database: {db_name}")
        else:
            logger.info(f"Test database already exists: {db_name}")
            
    except Exception as e:
        logger.error(f"Error creating test database {db_name}: {e}")
        raise
    finally:
        if conn:
            await conn.close()

async def _cleanup_test_database(db_name: str, config: TestDatabaseConfig):
    """Clean up test database."""
    conn = None
    try:
        conn = await asyncpg.connect(
            host=config.db_host,
            port=config.db_port,
            user=config.db_admin_user,
            password=config.db_admin_password,
            database="postgres"
        )
        
        # Terminate connections
        await conn.execute(f"""
            SELECT pg_terminate_backend(pg_stat_activity.pid)
            FROM pg_stat_activity
            WHERE pg_stat_activity.datname = '{db_name}'
            AND pid <> pg_backend_pid()
        """)
        
        # Drop database
        await conn.execute(f'DROP DATABASE IF EXISTS "{db_name}"')
        logger.info(f"Cleaned up test database: {db_name}")
        
    except Exception as e:
        logger.error(f"Error cleaning up test database {db_name}: {e}")
    finally:
        if conn:
            await conn.close()

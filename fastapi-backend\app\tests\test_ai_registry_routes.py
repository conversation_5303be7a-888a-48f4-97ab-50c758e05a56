"""
Integration tests for AI Models Registry Routes.
Tests the HTTP API endpoints for AI model registry management.
"""

import pytest
import json
from typing import Dict, Any, List
from unittest.mock import patch, AsyncMock, Mock
from fastapi.testclient import TestClient
from fastapi import status
from httpx import AsyncClient

# Import will be done within test functions to avoid circular imports
from tests.common.test_factory import DataFactory


@pytest.mark.asyncio
class TestAIRegistryRoutes:
    """Test cases for AI Models Registry Routes integration."""
    
    @pytest.fixture
    async def test_client(self):
        """Create test client for the FastAPI app."""
        from main import app
        async with Async<PERSON>lient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    async def authenticated_headers(self, test_data_factory: DataFactory):
        """Create authenticated headers for API requests."""
        # Create test user
        user = await test_data_factory.create_test_user(
            username="test_registry_user",
            role="ADMIN"
        )
        
        # Mock authentication token
        with patch('dependencies.auth.verify_token') as mock_verify:
            mock_verify.return_value = {
                "sub": user.username,
                "token_type": "access"
            }
            
            return {
                "Authorization": "Bearer test_token",
                "Content-Type": "application/json"
            }
    
    async def test_get_all_models(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test getting all AI models from registry."""
        # Setup test data - create some AI models
        model1 = await test_data_factory.create_test_ai_model(
            model_type="ocr",
            model_name="Advanced OCR Model",
            model_id="advanced_ocr_v1",
            deployment_status="active"
        )
        model2 = await test_data_factory.create_test_ai_model(
            model_type="caption",
            model_name="Caption Generator",
            model_id="caption_gen_v2",
            deployment_status="inactive"
        )
        
        # Make API request
        response = await test_client.get(
            "/api/v1/ai-models/",
            headers=authenticated_headers
        )
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        
        assert isinstance(result, list)
        assert len(result) >= 2
        
        # Verify model data structure
        model_ids = [model["model_id"] for model in result]
        assert "advanced_ocr_v1" in model_ids
        assert "caption_gen_v2" in model_ids
        
        # Verify response structure
        for model in result:
            assert "id" in model
            assert "model_name" in model
            assert "model_id" in model
            assert "supported_file_types" in model
            assert "output_format" in model
            assert "deployment_status" in model
            assert "created_at" in model
            assert "updated_at" in model
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_get_active_models_only(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test getting only active AI models."""
        # Setup test data
        active_model = await test_data_factory.create_test_ai_model(
            model_type="ocr",
            model_name="Active OCR Model",
            model_id="active_ocr_v1",
            deployment_status="active"
        )
        inactive_model = await test_data_factory.create_test_ai_model(
            model_type="caption",
            model_name="Inactive Caption Model",
            model_id="inactive_caption_v1",
            deployment_status="inactive"
        )
        
        # Make API request with active_only=True
        response = await test_client.get(
            "/api/v1/ai-models/",
            params={"active_only": True},
            headers=authenticated_headers
        )
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        
        assert isinstance(result, list)
        
        # Verify only active models are returned
        for model in result:
            assert model["deployment_status"] == "active"
        
        # Verify our active model is included
        model_ids = [model["model_id"] for model in result]
        assert "active_ocr_v1" in model_ids
        assert "inactive_caption_v1" not in model_ids
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_get_model_by_id(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test getting a specific AI model by ID."""
        # Setup test data
        model = await test_data_factory.create_test_ai_model(
            model_type="vqa",
            model_name="VQA Model",
            model_id="vqa_model_v1",
            description="Visual Question Answering model"
        )
        
        # Make API request
        response = await test_client.get(
            f"/api/v1/ai-models/{model.id}",
            headers=authenticated_headers
        )
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        
        assert result["id"] == model.id
        assert result["model_name"] == "VQA Model"
        assert result["model_id"] == "vqa_model_v1"
        assert result["description"] == "Visual Question Answering model"
        assert "created_at" in result
        assert "updated_at" in result
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_create_new_model(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test creating a new AI model."""
        # Prepare model data
        model_data = {
            "model_name": "New OCR Model",
            "model_id": "new_ocr_v1",
            "supported_file_types": ["jpg", "png", "pdf"],
            "output_format": {
                "type": "text",
                "fields": ["extracted_text", "confidence"]
            },
            "deployment_status": "active",
            "description": "A new OCR model for testing",
            "input_requirements": {
                "max_file_size": "10MB",
                "min_resolution": {"width": 100, "height": 100}
            }
        }
        
        # Make API request
        response = await test_client.post(
            "/api/v1/ai-models/",
            json=model_data,
            headers=authenticated_headers
        )
        
        # Verify response
        assert response.status_code == status.HTTP_201_CREATED
        result = response.json()
        
        assert result["model_name"] == "New OCR Model"
        assert result["model_id"] == "new_ocr_v1"
        assert result["supported_file_types"] == ["jpg", "png", "pdf"]
        assert result["deployment_status"] == "active"
        assert result["description"] == "A new OCR model for testing"
        assert "id" in result
        assert "created_at" in result
        assert "updated_at" in result
        
        # Verify input requirements
        assert result["input_requirements"]["max_file_size"] == "10MB"
        assert result["input_requirements"]["min_resolution"]["width"] == 100
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_create_duplicate_model_error(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test error when creating model with duplicate model_id."""
        # Setup test data - create existing model
        existing_model = await test_data_factory.create_test_ai_model(
            model_type="ocr",
            model_name="Existing OCR Model",
            model_id="duplicate_ocr_v1"
        )
        
        # Prepare model data with same model_id
        model_data = {
            "model_name": "Another OCR Model",
            "model_id": "duplicate_ocr_v1",  # Same as existing
            "supported_file_types": ["jpg", "png"],
            "output_format": {"type": "text"}
        }
        
        # Make API request
        response = await test_client.post(
            "/api/v1/ai-models/",
            json=model_data,
            headers=authenticated_headers
        )
        
        # Verify error response
        assert response.status_code == status.HTTP_409_CONFLICT
        result = response.json()
        assert "already exists" in result["detail"]
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_update_model(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test updating an existing AI model."""
        # Setup test data
        model = await test_data_factory.create_test_ai_model(
            model_type="caption",
            model_name="Original Caption Model",
            model_id="caption_v1",
            deployment_status="inactive"
        )
        
        # Prepare update data
        update_data = {
            "model_name": "Updated Caption Model",
            "deployment_status": "active",
            "description": "Updated description for caption model"
        }
        
        # Make API request
        response = await test_client.put(
            f"/api/v1/ai-models/{model.id}",
            json=update_data,
            headers=authenticated_headers
        )
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        
        assert result["id"] == model.id
        assert result["model_name"] == "Updated Caption Model"
        assert result["model_id"] == "caption_v1"  # Should remain unchanged
        assert result["deployment_status"] == "active"
        assert result["description"] == "Updated description for caption model"
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_delete_model(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory
    ):
        """Test deleting an AI model."""
        # Setup test data
        model = await test_data_factory.create_test_ai_model(
            model_type="transcription",
            model_name="Test Transcription Model",
            model_id="transcription_v1"
        )
        
        # Make API request
        response = await test_client.delete(
            f"/api/v1/ai-models/{model.id}",
            headers=authenticated_headers
        )
        
        # Verify response
        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        assert result["message"] == "Model deleted successfully"
        
        # Verify model is deleted - should return 404
        get_response = await test_client.get(
            f"/api/v1/ai-models/{model.id}",
            headers=authenticated_headers
        )
        assert get_response.status_code == status.HTTP_404_NOT_FOUND
        
        # Cleanup
        await test_data_factory.cleanup_created_data()

    async def test_get_nonexistent_model_error(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str]
    ):
        """Test error when getting non-existent model."""
        # Make API request with non-existent ID
        response = await test_client.get(
            "/api/v1/ai-models/99999",
            headers=authenticated_headers
        )

        # Verify error response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        result = response.json()
        assert "not found" in result["detail"].lower()

    async def test_update_nonexistent_model_error(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str]
    ):
        """Test error when updating non-existent model."""
        # Prepare update data
        update_data = {
            "model_name": "Updated Model",
            "deployment_status": "active"
        }

        # Make API request with non-existent ID
        response = await test_client.put(
            "/api/v1/ai-models/99999",
            json=update_data,
            headers=authenticated_headers
        )

        # Verify error response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        result = response.json()
        assert "not found" in result["detail"].lower()

    async def test_delete_nonexistent_model_error(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str]
    ):
        """Test error when deleting non-existent model."""
        # Make API request with non-existent ID
        response = await test_client.delete(
            "/api/v1/ai-models/99999",
            headers=authenticated_headers
        )

        # Verify error response
        assert response.status_code == status.HTTP_404_NOT_FOUND
        result = response.json()
        assert "not found" in result["detail"].lower()

    async def test_create_model_validation_error(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str]
    ):
        """Test validation error when creating model with invalid data."""
        # Prepare invalid model data (missing required fields)
        invalid_model_data = {
            "model_name": "Invalid Model",
            # Missing required fields: model_id, supported_file_types, output_format
        }

        # Make API request
        response = await test_client.post(
            "/api/v1/ai-models/",
            json=invalid_model_data,
            headers=authenticated_headers
        )

        # Verify validation error response
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        result = response.json()
        assert "detail" in result
        assert isinstance(result["detail"], list)

    async def test_unauthorized_access(
        self,
        test_client: AsyncClient
    ):
        """Test unauthorized access to AI models registry."""
        # Make API request without authentication
        response = await test_client.get("/api/v1/ai-models/")

        # Verify unauthorized response
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

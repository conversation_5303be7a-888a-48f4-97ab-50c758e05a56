"""
Service for managing project users with dynamic roles based on allocation strategies.
"""

import logging
from typing import List, Dict, Any, Optional
from sqlalchemy import select, insert, update, delete
from sqlalchemy.ext.asyncio import AsyncSession

from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies
from core.session_manager import get_project_db_session

logger = logging.getLogger(__name__)

class ProjectUsersService:
    """
    Service for managing project users with dynamic roles based on allocation strategies.
    """
    
    def __init__(self):
        pass
    
    async def get_project_strategy(self, project_code: str) -> Optional[AllocationStrategies]:
        """
        Get the allocation strategy for a project.
        
        Args:
            project_code: Project code
            
        Returns:
            Optional[AllocationStrategies]: The project's allocation strategy, or None if not found
        """
        try:
            async with get_master_db_context() as session:
                # Get project info
                result = await session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = result.scalar_one_or_none()
                
                if not project or not project.allocation_strategy_id:
                    return None
                
                # Get strategy info
                strategy_result = await session.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                )
                strategy = strategy_result.scalar_one_or_none()
                
                return strategy
                
        except Exception as e:
            logger.error(f"Error getting project strategy: {str(e)}")
            return None
    
    async def get_available_roles(self, project_code: str) -> Dict[str, Any]:
        """
        Get available roles for a project based on its allocation strategy.
        
        Args:
            project_code: Project code
            
        Returns:
            Dict: Available roles information
        """
        try:
            # Get the project's allocation strategy
            strategy = await self.get_project_strategy(project_code)
            if not strategy:
                return {
                    "success": False,
                    "error": f"No allocation strategy found for project {project_code}"
                }
            
            # Define base roles
            roles = ["admin", "project_manager"]
            
            # Add annotator roles based on strategy
            for i in range(1, strategy.num_annotators + 1):
                roles.append(f"annotator_{i}")
            
            # Add verifier role if required
            if strategy.requires_verification:
                roles.append("verifier")
            
            # Add AI role if required
            if strategy.requires_ai_preprocessing:
                roles.append("ai_processor")
            
            return {
                "success": True,
                "roles": roles,
                "strategy": {
                    "id": strategy.id,
                    "name": strategy.strategy_name,
                    "type": strategy.strategy_type
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting available roles: {str(e)}")
            return {
                "success": False,
                "error": f"Error getting available roles: {str(e)}"
            }
    
    async def add_user_to_project(
        self, 
        project_code: str, 
        user_id: int, 
        username: str, 
        role: str
    ) -> Dict[str, Any]:
        """
        Add a user to a project with a specific role.
        
        Args:
            project_code: Project code
            user_id: User ID
            username: Username
            role: Role to assign
            
        Returns:
            Dict: Result with success status and details
        """
        try:
            # Validate role against available roles
            roles_result = await self.get_available_roles(project_code)
            if not roles_result["success"]:
                return roles_result
            
            if role not in roles_result["roles"]:
                return {
                    "success": False,
                    "error": f"Invalid role '{role}' for project {project_code}. Available roles: {', '.join(roles_result['roles'])}"
                }
            
            # Get the project database session
            async with get_project_db_session(project_code) as session:
                # Check if user already exists in project
                from post_db.allocation_models.project_users import ProjectUsers
                result = await session.execute(
                    select(ProjectUsers).where(
                        ProjectUsers.user_id == user_id,
                        ProjectUsers.username == username
                    )
                )
                existing_user = result.scalar_one_or_none()
                
                if existing_user:
                    # Update existing user's role
                    await session.execute(
                        update(ProjectUsers)
                        .where(ProjectUsers.id == existing_user.id)
                        .values(role=role)
                    )
                    await session.commit()
                    
                    return {
                        "success": True,
                        "message": f"Updated user {username} (ID: {user_id}) with role '{role}' in project {project_code}",
                        "user_id": user_id,
                        "username": username,
                        "role": role,
                        "updated": True
                    }
                else:
                    # Add new user
                    result = await session.execute(
                        insert(ProjectUsers)
                        .values(
                            user_id=user_id,
                            username=username,
                            role=role
                        )
                        .returning(ProjectUsers.id)
                    )
                    new_user_id = result.scalar_one()
                    await session.commit()
                    
                    return {
                        "success": True,
                        "message": f"Added user {username} (ID: {user_id}) with role '{role}' to project {project_code}",
                        "user_id": user_id,
                        "username": username,
                        "role": role,
                        "project_user_id": new_user_id,
                        "updated": False
                    }
                
        except Exception as e:
            logger.error(f"Error adding user to project: {str(e)}")
            return {
                "success": False,
                "error": f"Error adding user to project: {str(e)}"
            }
    
    async def get_project_users(self, project_code: str) -> Dict[str, Any]:
        """
        Get all users in a project.
        
        Args:
            project_code: Project code
            
        Returns:
            Dict: Project users information
        """
        try:
            # Get the project database session
            async with get_project_db_session(project_code) as session:
                from post_db.allocation_models.project_users import ProjectUsers
                result = await session.execute(
                    select(ProjectUsers)
                )
                users = result.scalars().all()
                
                # Get available roles
                roles_result = await self.get_available_roles(project_code)
                available_roles = roles_result.get("roles", []) if roles_result["success"] else []
                
                return {
                    "success": True,
                    "users": [
                        {
                            "id": user.id,
                            "user_id": user.user_id,
                            "username": user.username,
                            "role": user.role
                        }
                        for user in users
                    ],
                    "available_roles": available_roles,
                    "strategy": roles_result.get("strategy") if roles_result["success"] else None
                }
                
        except Exception as e:
            logger.error(f"Error getting project users: {str(e)}")
            return {
                "success": False,
                "error": f"Error getting project users: {str(e)}"
            }
    
    async def remove_user_from_project(
        self, 
        project_code: str, 
        user_id: int
    ) -> Dict[str, Any]:
        """
        Remove a user from a project.
        
        Args:
            project_code: Project code
            user_id: User ID
            
        Returns:
            Dict: Result with success status and details
        """
        try:
            # Get the project database session
            async with get_project_db_session(project_code) as session:
                from post_db.allocation_models.project_users import ProjectUsers
                result = await session.execute(
                    delete(ProjectUsers)
                    .where(ProjectUsers.user_id == user_id)
                    .returning(ProjectUsers.username)
                )
                deleted_username = result.scalar_one_or_none()
                
                if not deleted_username:
                    return {
                        "success": False,
                        "error": f"User with ID {user_id} not found in project {project_code}"
                    }
                
                await session.commit()
                
                return {
                    "success": True,
                    "message": f"Removed user {deleted_username} (ID: {user_id}) from project {project_code}",
                    "user_id": user_id,
                    "username": deleted_username
                }
                
        except Exception as e:
            logger.error(f"Error removing user from project: {str(e)}")
            return {
                "success": False,
                "error": f"Error removing user from project: {str(e)}"
            }

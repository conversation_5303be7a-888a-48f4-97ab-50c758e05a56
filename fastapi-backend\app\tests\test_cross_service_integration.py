"""
Cross-service Integration Tests.
Tests the integration between different AI services and components.
"""

import pytest
import json
import asyncio
from typing import Dict, Any, List
from unittest.mock import patch, AsyncMock, Mock
from fastapi.testclient import TestClient
from fastapi import status
from httpx import AsyncClient

# Import will be done within test functions to avoid circular imports
from tests.common.mock_services import create_mock_services, patch_ai_services, patch_storage_connector
from tests.common.test_factory import DataFactory


@pytest.mark.asyncio
class TestCrossServiceIntegration:
    """Test cases for cross-service integration scenarios."""
    
    @pytest.fixture
    async def test_client(self):
        """Create test client for the FastAPI app."""
        from main import app
        async with Async<PERSON>lient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    async def authenticated_headers(self, test_data_factory: DataFactory):
        """Create authenticated headers for API requests."""
        # Create test user
        user = await test_data_factory.create_test_user(
            username="test_integration_user",
            role="ADMIN"
        )
        
        # Mock authentication token
        with patch('dependencies.auth.verify_token') as mock_verify:
            mock_verify.return_value = {
                "sub": user.username,
                "token_type": "access"
            }
            
            return {
                "Authorization": "Bearer test_token",
                "Content-Type": "application/json"
            }
    
    async def test_end_to_end_ai_processing_workflow(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test complete end-to-end AI processing workflow."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=3)
        project = scenario["project"]
        
        # Create AI models in registry
        ocr_model = await test_data_factory.create_test_ai_model(
            model_type="ocr",
            model_name="Production OCR Model",
            model_id="prod_ocr_v1",
            deployment_status="active"
        )
        
        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])
        
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_integration_user"}
            
            # Step 1: Verify AI model exists in registry
            models_response = await test_client.get(
                "/api/v1/ai-models/",
                params={"active_only": True},
                headers=authenticated_headers
            )
            assert models_response.status_code == status.HTTP_200_OK
            models = models_response.json()
            model_ids = [m["model_id"] for m in models]
            assert "prod_ocr_v1" in model_ids
            
            # Step 2: Process files using AI processing route
            form_data = {
                "project_code": project.project_code,
                "model_name": "prod_ocr_v1",
                "custom_prompt": "Extract all text accurately"
            }
            
            processing_response = await test_client.post(
                "/api/v1/ai/ocr/batch/project",
                data=form_data,
                headers=authenticated_headers
            )
            assert processing_response.status_code == status.HTTP_200_OK
            processing_result = processing_response.json()
            
            assert processing_result["status"] == "completed"
            assert processing_result["model_name"] == "prod_ocr_v1"
            assert processing_result["total_files"] == 3
            
            # Step 3: Verify processing results are stored
            results_response = await test_client.get(
                "/api/v1/ai/processing/results",
                params={
                    "project_code": project.project_code,
                    "processing_type": "ocr",
                    "limit": 10
                },
                headers=authenticated_headers
            )
            assert results_response.status_code == status.HTTP_200_OK
            results = results_response.json()
            
            assert results["status"] == "success"
            assert results["count"] >= 3
            assert len(results["results"]) >= 3
            
            # Step 4: Verify file allocations are created
            allocations_response = await test_client.get(
                "/api/v1/ai/file-allocations",
                params={
                    "project_code": project.project_code,
                    "processing_type": "ocr",
                    "limit": 10
                },
                headers=authenticated_headers
            )
            assert allocations_response.status_code == status.HTTP_200_OK
            allocations = allocations_response.json()
            
            assert allocations["status"] == "success"
            assert "results" in allocations
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_multi_model_processing_workflow(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test processing same files with multiple AI models."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=2)
        project = scenario["project"]
        
        # Create multiple AI models
        ocr_model = await test_data_factory.create_test_ai_model(
            model_type="ocr",
            model_name="OCR Model",
            model_id="ocr_v1",
            deployment_status="active"
        )
        caption_model = await test_data_factory.create_test_ai_model(
            model_type="caption",
            model_name="Caption Model",
            model_id="caption_v1",
            deployment_status="active"
        )
        
        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])
        
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_integration_user"}
            
            # Process with OCR model
            ocr_form_data = {
                "project_code": project.project_code,
                "model_name": "ocr_v1",
                "custom_prompt": "Extract text"
            }
            
            ocr_response = await test_client.post(
                "/api/v1/ai/ocr/batch/project",
                data=ocr_form_data,
                headers=authenticated_headers
            )
            assert ocr_response.status_code == status.HTTP_200_OK
            ocr_result = ocr_response.json()
            assert ocr_result["status"] == "completed"
            assert ocr_result["processing_type"] == "ocr"
            
            # Process with Caption model
            caption_form_data = {
                "project_code": project.project_code,
                "model_name": "caption_v1",
                "custom_prompt": "Generate captions"
            }
            
            caption_response = await test_client.post(
                "/api/v1/ai/caption/batch/project",
                data=caption_form_data,
                headers=authenticated_headers
            )
            assert caption_response.status_code == status.HTTP_200_OK
            caption_result = caption_response.json()
            assert caption_result["status"] == "completed"
            assert caption_result["processing_type"] == "caption"
            
            # Verify both processing types in results
            results_response = await test_client.get(
                "/api/v1/ai/processing/results",
                params={
                    "project_code": project.project_code,
                    "limit": 20
                },
                headers=authenticated_headers
            )
            assert results_response.status_code == status.HTTP_200_OK
            results = results_response.json()
            
            # Should have results from both OCR and Caption processing
            assert results["count"] >= 4  # 2 files × 2 models
            
            # Verify different processing types exist
            processing_types = set()
            for result in results["results"]:
                if "model_name" in result:
                    if "ocr" in result["model_name"]:
                        processing_types.add("ocr")
                    elif "caption" in result["model_name"]:
                        processing_types.add("caption")
            
            assert len(processing_types) >= 1  # At least one type should be identifiable
        
        # Cleanup
        await test_data_factory.cleanup_created_data()
    
    async def test_model_registry_and_processing_integration(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test integration between model registry and processing services."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=1)
        project = scenario["project"]
        
        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])
        
        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_integration_user"}
            
            # Step 1: Create a new model via registry API
            model_data = {
                "model_name": "Dynamic VQA Model",
                "model_id": "dynamic_vqa_v1",
                "supported_file_types": ["jpg", "png"],
                "output_format": {
                    "type": "qa",
                    "fields": ["question", "answer", "confidence"]
                },
                "deployment_status": "active",
                "description": "Dynamically created VQA model"
            }
            
            create_response = await test_client.post(
                "/api/v1/ai-models/",
                json=model_data,
                headers=authenticated_headers
            )
            assert create_response.status_code == status.HTTP_201_CREATED
            created_model = create_response.json()
            
            # Step 2: Use the newly created model for processing
            form_data = {
                "project_code": project.project_code,
                "model_name": "dynamic_vqa_v1",
                "question": "What is in this image?"
            }
            
            processing_response = await test_client.post(
                "/api/v1/ai/vqa/batch/project",
                data=form_data,
                headers=authenticated_headers
            )
            assert processing_response.status_code == status.HTTP_200_OK
            processing_result = processing_response.json()
            
            assert processing_result["status"] == "completed"
            assert processing_result["model_name"] == "dynamic_vqa_v1"
            
            # Step 3: Update model status to inactive
            update_data = {"deployment_status": "inactive"}
            update_response = await test_client.put(
                f"/api/v1/ai-models/{created_model['id']}",
                json=update_data,
                headers=authenticated_headers
            )
            assert update_response.status_code == status.HTTP_200_OK
            
            # Step 4: Verify model is no longer in active list
            active_models_response = await test_client.get(
                "/api/v1/ai-models/",
                params={"active_only": True},
                headers=authenticated_headers
            )
            assert active_models_response.status_code == status.HTTP_200_OK
            active_models = active_models_response.json()
            
            active_model_ids = [m["model_id"] for m in active_models]
            assert "dynamic_vqa_v1" not in active_model_ids
        
        # Cleanup
        await test_data_factory.cleanup_created_data()

    async def test_concurrent_processing_different_models(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test concurrent processing with different models."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=2)
        project = scenario["project"]

        # Create multiple models
        models = []
        for i, model_type in enumerate(["ocr", "caption", "vqa"]):
            model = await test_data_factory.create_test_ai_model(
                model_type=model_type,
                model_name=f"{model_type.upper()} Model {i+1}",
                model_id=f"{model_type}_concurrent_v{i+1}",
                deployment_status="active"
            )
            models.append(model)

        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])

        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_integration_user"}

            # Create concurrent processing tasks
            async def process_ocr():
                form_data = {
                    "project_code": project.project_code,
                    "model_name": "ocr_concurrent_v1",
                    "custom_prompt": "Extract text"
                }
                return await test_client.post(
                    "/api/v1/ai/ocr/batch/project",
                    data=form_data,
                    headers=authenticated_headers
                )

            async def process_caption():
                form_data = {
                    "project_code": project.project_code,
                    "model_name": "caption_concurrent_v2",
                    "custom_prompt": "Generate captions"
                }
                return await test_client.post(
                    "/api/v1/ai/caption/batch/project",
                    data=form_data,
                    headers=authenticated_headers
                )

            async def process_vqa():
                form_data = {
                    "project_code": project.project_code,
                    "model_name": "vqa_concurrent_v3",
                    "question": "What do you see?"
                }
                return await test_client.post(
                    "/api/v1/ai/vqa/batch/project",
                    data=form_data,
                    headers=authenticated_headers
                )

            # Execute concurrent processing
            ocr_response, caption_response, vqa_response = await asyncio.gather(
                process_ocr(),
                process_caption(),
                process_vqa(),
                return_exceptions=True
            )

            # Verify all processing completed successfully
            for response in [ocr_response, caption_response, vqa_response]:
                if isinstance(response, Exception):
                    pytest.fail(f"Processing failed with exception: {response}")
                assert response.status_code == status.HTTP_200_OK
                result = response.json()
                assert result["status"] == "completed"

            # Verify results are stored for all processing types
            results_response = await test_client.get(
                "/api/v1/ai/processing/results",
                params={
                    "project_code": project.project_code,
                    "limit": 20
                },
                headers=authenticated_headers
            )
            assert results_response.status_code == status.HTTP_200_OK
            results = results_response.json()

            # Should have results from all three processing types
            assert results["count"] >= 6  # 2 files × 3 models

        # Cleanup
        await test_data_factory.cleanup_created_data()

    async def test_error_handling_across_services(
        self,
        test_client: AsyncClient,
        authenticated_headers: Dict[str, str],
        test_data_factory: DataFactory,
        monkeypatch
    ):
        """Test error handling across different services."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=1)
        project = scenario["project"]

        # Create a model that will be deleted
        model = await test_data_factory.create_test_ai_model(
            model_type="ocr",
            model_name="Temporary OCR Model",
            model_id="temp_ocr_v1",
            deployment_status="active"
        )

        # Setup mocks
        mock_services = create_mock_services()
        patch_ai_services(monkeypatch, mock_services)
        patch_storage_connector(monkeypatch, mock_services["storage"])

        # Mock authentication
        with patch('dependencies.auth.get_current_active_user') as mock_auth:
            mock_auth.return_value = {"sub": "test_integration_user"}

            # Step 1: Verify model exists and is active
            models_response = await test_client.get(
                "/api/v1/ai-models/",
                params={"active_only": True},
                headers=authenticated_headers
            )
            assert models_response.status_code == status.HTTP_200_OK
            models = models_response.json()
            model_ids = [m["model_id"] for m in models]
            assert "temp_ocr_v1" in model_ids

            # Step 2: Delete the model
            delete_response = await test_client.delete(
                f"/api/v1/ai-models/{model.id}",
                headers=authenticated_headers
            )
            assert delete_response.status_code == status.HTTP_200_OK

            # Step 3: Try to use the deleted model for processing
            form_data = {
                "project_code": project.project_code,
                "model_name": "temp_ocr_v1",
                "custom_prompt": "Extract text"
            }

            # This should still work as the processing service doesn't validate
            # model existence in registry (it uses the model name directly)
            processing_response = await test_client.post(
                "/api/v1/ai/ocr/batch/project",
                data=form_data,
                headers=authenticated_headers
            )
            # The processing might still succeed as it doesn't check registry
            assert processing_response.status_code in [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND]

            # Step 4: Verify model is no longer in registry
            get_model_response = await test_client.get(
                f"/api/v1/ai-models/{model.id}",
                headers=authenticated_headers
            )
            assert get_model_response.status_code == status.HTTP_404_NOT_FOUND

        # Cleanup
        await test_data_factory.cleanup_created_data()

    async def test_authentication_across_services(
        self,
        test_client: AsyncClient,
        test_data_factory: DataFactory
    ):
        """Test authentication requirements across different services."""
        # Setup test data
        scenario = await test_data_factory.create_complete_test_scenario(num_files=1)
        project = scenario["project"]

        # Test unauthenticated access to various endpoints
        endpoints_to_test = [
            ("GET", "/api/v1/ai-models/"),
            ("GET", "/api/v1/ai/projects"),
            ("GET", "/api/v1/ai/processing/results"),
            ("GET", "/api/v1/ai/file-allocations"),
        ]

        for method, endpoint in endpoints_to_test:
            if method == "GET":
                response = await test_client.get(endpoint)
            else:
                response = await test_client.post(endpoint, json={})

            # Most endpoints should require authentication
            # Some might return 401, others might work without auth
            assert response.status_code in [
                status.HTTP_200_OK,  # Some endpoints might not require auth
                status.HTTP_401_UNAUTHORIZED,  # Most should require auth
                status.HTTP_403_FORBIDDEN,  # Some might be forbidden
                status.HTTP_422_UNPROCESSABLE_ENTITY  # Invalid data
            ]

        # Cleanup
        await test_data_factory.cleanup_created_data()

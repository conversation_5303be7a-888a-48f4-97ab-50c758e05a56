"""
Repository for batch assignment operations.
Handles database operations for assigning annotators to batches.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, and_
from sqlalchemy.orm import selectinload
from datetime import datetime

from core.session_manager import get_project_db_session
from post_db.allocation_models.allocation_batches import AllocationBatches
from post_db.allocation_models.files_registry import FilesRegistry
from post_db.allocation_models.file_allocations import FileAllocations, WorkflowPhase
from post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
from post_db.allocation_models.project_users import ProjectUsers

logger = logging.getLogger(__name__)

class BatchAssignmentRepository:
    """Repository for batch assignment database operations."""
    
    def __init__(self):
        pass
    
    async def get_user_current_batch(self, project_code: str, user_id: int) -> Optional[int]:
        """
        Get the current batch ID for a user.
        
        Args:
            project_code: Project code
            user_id: User ID
            
        Returns:
            Optional[int]: Current batch ID or None if no active batch
        """
        try:
            async with get_project_db_session(project_code) as session:
                result = await session.execute(
                    select(ProjectUsers.current_batch)
                    .where(ProjectUsers.user_id == user_id)
                )
                current_batch = result.scalar_one_or_none()
                return current_batch
        except Exception as e:
            logger.error(f"Error getting user current batch: {str(e)}")
            return None
    
    async def get_available_batches(self, project_code: str) -> List[AllocationBatches]:
        """
        Get batches that need more annotators, ordered by batch_id.
        
        Args:
            project_code: Project code
            
        Returns:
            List[AllocationBatches]: Available batches (where assignment_count < annotation_count)
        """
        try:
            async with get_project_db_session(project_code) as session:
                result = await session.execute(
                    select(AllocationBatches)
                    .where(AllocationBatches.assignment_count < AllocationBatches.annotation_count)
                    .order_by(AllocationBatches.id)
                )
                batches = result.scalars().all()
                logger.info(f"Found {len(batches)} available batches (assignment_count < annotation_count)")
                return batches
        except Exception as e:
            logger.error(f"Error getting available batches: {str(e)}")
            return []
    
    async def get_batch_files(self, project_code: str, batch_id: int) -> List[FilesRegistry]:
        """
        Get all files in a batch.
        
        Args:
            project_code: Project code
            batch_id: Batch ID
            
        Returns:
            List[FilesRegistry]: Files in the batch
        """
        try:
            async with get_project_db_session(project_code) as session:
                result = await session.execute(
                    select(FilesRegistry)
                    .where(FilesRegistry.batch_id == batch_id)
                    .order_by(FilesRegistry.id)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting batch files: {str(e)}")
            return []
    
    async def find_next_annotator_slot(self, project_code: str, batch_id: int) -> Optional[int]:
        """
        Find the next available annotator slot (annotator_1, annotator_2, etc.) in a batch.
        
        Args:
            project_code: Project code
            batch_id: Batch ID
            
        Returns:
            Optional[int]: Next available slot number (1, 2, 3, etc.) or None if all slots filled
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Use text query to ensure we get all columns including dynamic ones
                from sqlalchemy import text
                result = await session.execute(
                    text("SELECT * FROM allocation_batches WHERE id = :batch_id"),
                    {"batch_id": batch_id}
                )
                row = result.fetchone()
                
                if not row:
                    logger.error(f"Batch {batch_id} not found")
                    return None
                
                # Convert row to dict for easier access
                batch_dict = dict(row._mapping)
                logger.info(f"Batch columns: {list(batch_dict.keys())}")
                
                max_annotators = batch_dict.get('annotation_count', 0)
                logger.info(f"Checking annotator slots for batch {batch_id}, max_annotators: {max_annotators}")
                
                # Check each annotator slot using the dict
                for i in range(1, max_annotators + 1):
                    annotator_field = f"annotator_{i}"
                    annotator_value = batch_dict.get(annotator_field)
                    logger.info(f"Slot {i} ({annotator_field}): {annotator_value}")
                    if annotator_value is None:
                        logger.info(f"Found available slot: {i}")
                        return i
                
                logger.info(f"No available slots found in batch {batch_id}")
                return None
        except Exception as e:
            logger.error(f"Error finding next annotator slot: {str(e)}")
            return None
    
    async def assign_user_to_batch(
        self, 
        project_code: str, 
        user_id: int, 
        username: str, 
        batch_id: int, 
        annotator_slot: int,
        total_files: int
    ) -> bool:
        """
        Assign a user to a batch by updating all relevant tables.
        
        Args:
            project_code: Project code
            user_id: User ID
            username: Username
            batch_id: Batch ID
            annotator_slot: Annotator slot number (1, 2, 3, etc.)
            total_files: Total files in the batch
            
        Returns:
            bool: Success status
        """
        try:
            logger.info(f"Starting database assignment: user {user_id} -> batch {batch_id}, slot {annotator_slot}")
            async with get_project_db_session(project_code) as session:
                # 1. Update allocation_batches table using raw SQL
                annotator_field = f"annotator_{annotator_slot}"
                logger.info(f"Updating allocation_batches: {annotator_field} = {user_id}")
                
                from sqlalchemy import text
                update_result = await session.execute(
                    text(f"""
                        UPDATE allocation_batches 
                        SET {annotator_field} = :user_id, 
                            assignment_count = assignment_count + 1 
                        WHERE id = :batch_id
                    """),
                    {"user_id": user_id, "batch_id": batch_id}
                )
                logger.info(f"Updated allocation_batches, rows affected: {update_result.rowcount}")
                
                # 2. Update project_users table
                logger.info(f"Updating project_users: current_batch = {batch_id}")
                project_users_result = await session.execute(
                    update(ProjectUsers)
                    .where(ProjectUsers.user_id == user_id)
                    .values(current_batch=batch_id)
                )
                logger.info(f"Updated project_users, rows affected: {project_users_result.rowcount}")
                
                # 3. Create user_allocations record
                logger.info(f"Creating user_allocations record")
                user_allocation = UserAllocations(
                    user_id=user_id,
                    batch_id=batch_id,
                    username=username,
                    total_files=total_files,
                    allocation_role=AllocationRole.ANNOTATOR,
                    is_active=True,
                    allocated_at=datetime.utcnow()
                )
                session.add(user_allocation)
                logger.info(f"Added user_allocations record")
                
                # 4. Update file_allocations table for all files in the batch
                files_result = await session.execute(
                    select(FilesRegistry)
                    .where(FilesRegistry.batch_id == batch_id)
                )
                batch_files = files_result.scalars().all()
                logger.info(f"Found {len(batch_files)} files in batch {batch_id}")
                
                # Debug: Check existing file allocations for this batch
                # First, check what columns exist in the table to avoid column not found errors
                try:
                    # Try to get table structure to build dynamic query
                    table_info = await session.execute(
                        text("SELECT column_name FROM information_schema.columns WHERE table_name = 'file_allocations' AND table_schema = current_schema() ORDER BY ordinal_position")
                    )
                    available_columns = [row[0] for row in table_info.fetchall()]
                    logger.info(f"Available columns in file_allocations: {available_columns}")
                    
                    # Build dynamic SELECT clause based on available columns
                    base_columns = ['file_id', 'allocation_sequence']
                    optional_columns = ['annotator_1', 'annotator_2', 'annotator_3', 'verifier']
                    
                    select_columns = base_columns.copy()
                    for col in optional_columns:
                        if col in available_columns:
                            select_columns.append(col)
                    
                    select_clause = ', '.join(select_columns)
                    logger.info(f"Using SELECT clause: {select_clause}")
                    
                    existing_allocations = await session.execute(
                        text(f"SELECT {select_clause} FROM file_allocations WHERE batch_id = :batch_id ORDER BY file_id"),
                        {"batch_id": batch_id}
                    )
                    existing_records = existing_allocations.fetchall()
                    logger.info(f"Found {len(existing_records)} existing file_allocations for batch {batch_id}")
                    if existing_records:
                        logger.info(f"Sample existing records: {existing_records[:3]}")
                        
                except Exception as column_error:
                    logger.warning(f"Could not query file_allocations table structure: {str(column_error)}")
                    # Fallback to basic query without optional columns
                    existing_allocations = await session.execute(
                        text("SELECT file_id, allocation_sequence FROM file_allocations WHERE batch_id = :batch_id ORDER BY file_id"),
                        {"batch_id": batch_id}
                    )
                    existing_records = existing_allocations.fetchall()
                    logger.info(f"Found {len(existing_records)} existing file_allocations for batch {batch_id} (basic query)")
                    if existing_records:
                        logger.info(f"Sample existing records: {existing_records[:3]}")
                
                files_created = 0
                files_skipped = 0
                
                for i, file in enumerate(batch_files):
                    # For parallel strategy: Always use allocation_sequence=1 (primary allocation)
                    # and update the appropriate annotator field based on the slot
                    primary_allocation_sequence = 1
                    
                    # Check if file allocation already exists for the primary sequence
                    existing_check = await session.execute(
                        text(f"SELECT id, {annotator_field} FROM file_allocations WHERE file_id = :file_id AND allocation_sequence = :allocation_sequence"),
                        {"file_id": file.id, "allocation_sequence": primary_allocation_sequence}
                    )
                    existing_allocation = existing_check.fetchone()
                    
                    if existing_allocation:
                        # Check if this annotator field is already assigned
                        existing_user_id = existing_allocation[1]  # annotator_field value
                        if existing_user_id == user_id:
                            files_skipped += 1
                            if i < 3:  # Log first few files for debugging
                                logger.info(f"User {user_id} already assigned to file {file.id} in slot {annotator_slot}")
                            continue
                        elif existing_user_id is not None:
                            # Annotator field already has a different user - this should not happen in normal flow
                            files_skipped += 1
                            if i < 3:  # Log first few files for debugging
                                logger.warning(f"Annotator slot {annotator_slot} for file {file.id} already occupied by user {existing_user_id}")
                            continue
                        else:
                            # Update existing record with the new user in the appropriate annotator slot
                            files_created += 1
                            if i < 3:  # Log first few files for debugging
                                logger.info(f"Updating file_allocation for file {file.id}: {annotator_field} = {user_id}")
                            
                            await session.execute(
                                text(f"""
                                    UPDATE file_allocations 
                                    SET {annotator_field} = :user_id, 
                                        allocated_at = COALESCE(allocated_at, :allocated_at),
                                        assignment_count = assignment_count + 1
                                    WHERE file_id = :file_id AND allocation_sequence = :allocation_sequence
                                """),
                                {
                                    "user_id": user_id,
                                    "allocated_at": datetime.utcnow(),
                                    "file_id": file.id,
                                    "allocation_sequence": primary_allocation_sequence
                                }
                            )
                    else:
                        # Create new file allocation record with the primary sequence
                        # This should only happen if file_allocations weren't created during file registration
                        files_created += 1
                        if i < 3:  # Log first few files for debugging
                            logger.info(f"Creating file_allocation for file {file.id}: {annotator_field} = {user_id}")
                        
                        await session.execute(
                            text(f"""
                                INSERT INTO file_allocations 
                                (file_id, batch_id, allocation_sequence, workflow_phase, processing_status, allocated_at, assignment_count, completion_count, {annotator_field})
                                VALUES (:file_id, :batch_id, :allocation_sequence, :workflow_phase, :processing_status, :allocated_at, :assignment_count, :completion_count, :user_id)
                            """),
                            {
                                "file_id": file.id,
                                "batch_id": batch_id,
                                "allocation_sequence": primary_allocation_sequence,  # Always use sequence 1 for parallel strategy
                                "workflow_phase": "annotation",
                                "processing_status": "pending",
                                "allocated_at": datetime.utcnow(),
                                "assignment_count": 1,
                                "completion_count": 0,  # Start with 0 completions
                                "user_id": user_id
                            }
                        )
                
                # Log summary of file allocations
                logger.info(f"File allocation summary: {files_created} created, {files_skipped} skipped")
                
                logger.info(f"Committing all changes to database...")
                await session.commit()
                logger.info(f"Successfully assigned user {user_id} to batch {batch_id} in slot {annotator_slot}")
                return True
                
        except Exception as e:
            logger.error(f"Error assigning user to batch: {str(e)}")
            return False
    
    async def get_batch_with_files(self, project_code: str, batch_id: int) -> Optional[Dict[str, Any]]:
        """
        Get batch details with all files for frontend display, including AI preprocessing results.
        
        Args:
            project_code: Project code
            batch_id: Batch ID
            
        Returns:
            Optional[Dict]: Batch details with files and AI suggestions or None if not found
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Get batch details
                batch_result = await session.execute(
                    select(AllocationBatches)
                    .where(AllocationBatches.id == batch_id)
                )
                batch = batch_result.scalar_one_or_none()
                
                if not batch:
                    return None
                
                # Get files in the batch with their allocations (for AI preprocessing results)
                files_result = await session.execute(
                    select(FilesRegistry, FileAllocations)
                    .join(FileAllocations, FilesRegistry.id == FileAllocations.file_id, isouter=True)
                    .where(FilesRegistry.batch_id == batch_id)
                    .order_by(FilesRegistry.id)
                )
                files_with_allocations = files_result.all()
                
                files_data = []
                for file, allocation in files_with_allocations:
                    file_data = {
                        "id": file.id,
                        "filename": file.original_filename,
                        "file_identifier": file.file_identifier,
                        "file_type": file.file_type,
                        "file_size": file.file_size_bytes,
                        "storage_location": file.storage_location
                    }
                    
                    # Debug logging
                    logger.info(f"DEBUG: File {file.id} ({file.original_filename})")
                    logger.info(f"DEBUG: Has allocation: {allocation is not None}")
                    if allocation:
                        logger.info(f"DEBUG: Allocation processing_status: {allocation.processing_status}")
                        logger.info(f"DEBUG: Allocation preprocessing_results: {allocation.preprocessing_results}")
                    
                    # Add AI preprocessing results if available
                    if allocation and allocation.preprocessing_results:
                        # Extract AI suggestions from the nested structure
                        preprocessing_data = allocation.preprocessing_results
                        ai_suggestions = None
                        
                        # Check if we have model_response with annotation_results
                        if (preprocessing_data.get("model_response") and 
                            preprocessing_data["model_response"].get("annotation_results")):
                            annotation_results = preprocessing_data["model_response"]["annotation_results"]
                            
                            # Extract just the answers from each field
                            ai_suggestions = {}
                            for field_name, field_data in annotation_results.items():
                                if isinstance(field_data, dict) and "answer" in field_data:
                                    ai_suggestions[field_name] = field_data["answer"]
                            
                            # If no answers found, use the whole annotation_results as fallback
                            if not ai_suggestions:
                                ai_suggestions = annotation_results
                                
                            logger.info(f"DEBUG: Extracted AI answers for file {file.id}: {ai_suggestions}")
                        else:
                            # Fallback: use the whole preprocessing_results as suggestions
                            ai_suggestions = preprocessing_data
                            logger.info(f"DEBUG: Using full preprocessing_results as AI suggestions for file {file.id}")
                        
                        file_data["ai_suggestions"] = ai_suggestions
                        file_data["processing_status"] = allocation.processing_status
                        logger.info(f"DEBUG: Added AI suggestions for file {file.id}: {ai_suggestions}")
                    else:
                        file_data["ai_suggestions"] = None
                        file_data["processing_status"] = "pending"
                        logger.info(f"DEBUG: No AI suggestions for file {file.id}")
                    
                    files_data.append(file_data)
                
                return {
                    "batch_id": batch.id,
                    "batch_identifier": batch.batch_identifier,
                    "batch_status": batch.batch_status,
                    "total_files": batch.total_files,
                    "assignment_count": batch.assignment_count,
                    "files": files_data
                }
                
        except Exception as e:
            logger.error(f"Error getting batch with files: {str(e)}")
            return None
    
    async def get_user_project_info(self, project_code: str, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user information from project database.
        
        Args:
            project_code: Project code
            user_id: User ID
            
        Returns:
            Optional[Dict]: User project info or None if not found
        """
        try:
            async with get_project_db_session(project_code) as session:
                result = await session.execute(
                    select(ProjectUsers)
                    .where(ProjectUsers.user_id == user_id)
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    return None
                
                return {
                    "user_id": user.user_id,
                    "username": user.username,
                    "role": user.role,
                    "current_batch": user.current_batch,
                    "completed_batches": user.completed_batches or []
                }
                
        except Exception as e:
            logger.error(f"Error getting user project info: {str(e)}")
            return None
    
    async def get_file_ai_suggestions(self, project_code: str, file_id: int) -> Optional[Dict[str, Any]]:
        """
        Get AI preprocessing results for a specific file.
        
        Args:
            project_code: Project code
            file_id: File ID
            
        Returns:
            Optional[Dict]: AI suggestions and processing status or None if not found
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Get file allocation with preprocessing results
                result = await session.execute(
                    select(FileAllocations.preprocessing_results, FileAllocations.processing_status)
                    .where(FileAllocations.file_id == file_id)
                    .limit(1)
                )
                allocation = result.first()
                
                if not allocation:
                    return None
                
                return {
                    "ai_suggestions": allocation[0],  # preprocessing_results
                    "processing_status": allocation[1]  # processing_status
                }
                
        except Exception as e:
            logger.error(f"Error getting file AI suggestions: {str(e)}")
            return None
